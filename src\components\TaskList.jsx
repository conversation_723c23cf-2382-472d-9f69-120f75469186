import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Slider,
  CircularProgress,
  IconButton,
  Tooltip,
  InputAdornment
} from '@mui/material';
import { styled } from '@mui/material/styles';
import api from '../Services/ApiService';
import { toast } from 'react-toastify';
import RefreshIcon from '@mui/icons-material/Refresh';
import SearchIcon from '@mui/icons-material/Search';
import CloseIcon from '@mui/icons-material/Close';
import '../Styles/TaskList.css';

// Custom styled components
const TaskPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  marginBottom: theme.spacing(3),
  borderRadius: theme.shape.borderRadius * 2,
  boxShadow: '0 4px 20px 0 rgba(0,0,0,0.05)',
}));

const TaskHeader = styled(Box)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  marginBottom: theme.spacing(2),
}));

const TaskList = () => {
  const [tasks, setTasks] = useState([]);
  const [filteredTasks, setFilteredTasks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [openDialog, setOpenDialog] = useState(false);
  const [dialogType, setDialogType] = useState(null);
  const [selectedTask, setSelectedTask] = useState(null);
  const [updateData, setUpdateData] = useState({
    status: '',
    progress: 0,
    notes: '',
    message: ''
  });
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  // Fetch tasks on component mount
  useEffect(() => {
    fetchTasks();
  }, []);

  // Component-specific search timeout reference
  const taskSearchTimeoutRef = useRef(null);

  // Cleanup function to clear timeout when component unmounts
  useEffect(() => {
    return () => {
      if (taskSearchTimeoutRef.current) {
        clearTimeout(taskSearchTimeoutRef.current);
      }
    };
  }, []);

  // Enhanced real-time search with debouncing for server-side search
  useEffect(() => {
    // Clear any existing timeout
    if (taskSearchTimeoutRef.current) {
      clearTimeout(taskSearchTimeoutRef.current);
    }

    if (searchQuery.trim() === '') {
      setFilteredTasks(tasks);
      return;
    }

    // For immediate client-side filtering
    const query = searchQuery.toLowerCase();
    const filtered = tasks.filter(task =>
      task.title.toLowerCase().includes(query) ||
      task.description.toLowerCase().includes(query) ||
      task.status.toLowerCase().includes(query) ||
      task.priority.toLowerCase().includes(query) ||
      (task.category && task.category.toLowerCase().includes(query))
    );
    setFilteredTasks(filtered);

    // Debounced server-side search for more accurate results
    taskSearchTimeoutRef.current = setTimeout(() => {
      if (searchQuery.trim() !== '') {
        handleServerSearch();
      }
    }, 300); // 300ms delay for more responsive feel
  }, [searchQuery, tasks]);

  const fetchTasks = async () => {
    try {
      setRefreshing(true);
      console.log('Fetching tasks...');

      // Try the primary endpoint
      try {
        const response = await api.get('/tasks/user/assigned');
        console.log('Tasks response from primary endpoint:', response.data);

        // Check if the response has the expected structure
        if (Array.isArray(response.data)) {
          setTasks(response.data);
          setFilteredTasks(response.data);
          return;
        } else if (response.data && Array.isArray(response.data.tasks)) {
          setTasks(response.data.tasks);
          setFilteredTasks(response.data.tasks);
          return;
        } else {
          console.warn('Unexpected data structure from primary endpoint:', response.data);
        }
      } catch (primaryError) {
        console.warn('Primary endpoint failed for tasks:', primaryError);
      }

      // If we get here, try the alternative endpoint
      try {
        const altResponse = await api.get('/normaluser/tasks');
        console.log('Tasks response from alternative endpoint:', altResponse.data);

        // Check if the response has the expected structure
        if (Array.isArray(altResponse.data)) {
          setTasks(altResponse.data);
          setFilteredTasks(altResponse.data);
          return;
        } else if (altResponse.data && Array.isArray(altResponse.data.tasks)) {
          setTasks(altResponse.data.tasks);
          setFilteredTasks(altResponse.data.tasks);
          return;
        } else {
          console.warn('Unexpected data structure from alternative endpoint:', altResponse.data);
        }
      } catch (altError) {
        console.error('Alternative endpoint also failed for tasks:', altError);
      }

      // If we get here, set an empty array
      setTasks([]);
      setFilteredTasks([]);
    } catch (error) {
      console.error('Error fetching tasks:', error);
      toast.error('Failed to load tasks');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleOpenDialog = (type, task = null) => {
    setDialogType(type);
    setSelectedTask(task);

    if (type === 'update' && task) {
      setUpdateData({
        status: task.status || '',
        progress: task.progress || 0,
        notes: task.notes || '',
        message: ''
      });
    }

    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setDialogType(null);
    setSelectedTask(null);
    setUpdateData({
      status: '',
      progress: 0,
      notes: '',
      message: ''
    });
  };

  const handleUpdateTask = async () => {
    if (!selectedTask) return;

    try {
      console.log('Updating task:', selectedTask._id, updateData);

      // Try the primary endpoint - using the status update endpoint
      try {
        const response = await api.patch(`/tasks/${selectedTask._id}/status`, {
          status: updateData.status,
          progress: updateData.progress,
          message: updateData.notes || 'Task updated by user'
        });
        console.log('Task update response from primary endpoint:', response.data);
        toast.success('Task updated successfully');
        fetchTasks();
        handleCloseDialog();
        return;
      } catch (primaryError) {
        console.warn('Primary endpoint failed for task update:', primaryError);

        // Check for specific error messages
        const errorMessage = primaryError.response?.data?.message || 'Unknown error';
        const errorDetails = primaryError.response?.data?.details || '';

        if (primaryError.response?.status === 403) {
          toast.error(`Permission denied: ${errorMessage}`);
          handleCloseDialog();
          return;
        }
      }

      // If we get here, try the alternative endpoint using the status update endpoint
      try {
        // Use the task status update endpoint which is available for normal users
        const altResponse = await api.patch(`/tasks/${selectedTask._id}/status`, {
          status: updateData.status,
          progress: updateData.progress,
          message: updateData.notes || 'Task updated by user'
        });
        console.log('Task update response from alternative endpoint:', altResponse.data);
        toast.success('Task updated successfully');
        fetchTasks();
        handleCloseDialog();
        return;
      } catch (altError) {
        console.error('Alternative endpoint also failed for task update:', altError);

        // Check for specific error messages
        const errorMessage = altError.response?.data?.message || 'Unknown error';
        const errorDetails = altError.response?.data?.details || '';

        if (altError.response?.status === 403) {
          toast.error(`Permission denied: ${errorMessage}`);
          handleCloseDialog();
          return;
        }

        throw altError; // Re-throw to be caught by the outer catch
      }
    } catch (error) {
      console.error('Error updating task:', error);

      // Check for specific error messages
      const errorMessage = error.response?.data?.message || 'Unknown error';
      const errorDetails = error.response?.data?.details || '';

      if (error.response?.status === 403) {
        toast.error(`Permission denied: ${errorMessage}`);
      } else {
        toast.error(`Failed to update task: ${errorMessage}`);
      }
    }
  };

  const handleCompleteTask = async (taskId) => {
    try {
      console.log('Completing task:', taskId);

      // Try the primary endpoint - using the status update endpoint
      try {
        const response = await api.patch(`/tasks/${taskId}/status`, {
          status: 'Completed',
          progress: 100,
          message: 'Task marked as completed by user'
        });
        console.log('Task completion response from primary endpoint:', response.data);
        toast.success('Task marked as completed');
        fetchTasks();
        return;
      } catch (primaryError) {
        console.warn('Primary endpoint failed for task completion:', primaryError);

        // Check for specific error messages
        const errorMessage = primaryError.response?.data?.message || 'Unknown error';
        const errorDetails = primaryError.response?.data?.details || '';

        if (primaryError.response?.status === 403) {
          toast.error(`Permission denied: ${errorMessage}`);
          return;
        }
      }

      // If we get here, try the alternative endpoint using the status update endpoint
      try {
        // Use the task status update endpoint which is available for normal users
        const altResponse = await api.patch(`/tasks/${taskId}/status`, {
          status: 'Completed',
          progress: 100,
          message: 'Task marked as completed by user'
        });
        console.log('Task completion response from alternative endpoint:', altResponse.data);
        toast.success('Task marked as completed');
        fetchTasks();
        return;
      } catch (altError) {
        console.error('Alternative endpoint also failed for task completion:', altError);

        // Check for specific error messages
        const errorMessage = altError.response?.data?.message || 'Unknown error';
        const errorDetails = altError.response?.data?.details || '';

        if (altError.response?.status === 403) {
          toast.error(`Permission denied: ${errorMessage}`);
          return;
        }

        throw altError; // Re-throw to be caught by the outer catch
      }
    } catch (error) {
      console.error('Error completing task:', error);
      toast.error('Failed to complete task: ' + (error.response?.data?.message || 'Unknown error'));
    }
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';

    try {
      const date = new Date(dateString);
      return date.toLocaleDateString();
    } catch (error) {
      return dateString;
    }
  };

  // Get status color
  const getStatusColor = (status) => {
    switch (status) {
      case 'Not Started':
        return 'default';
      case 'In Progress':
        return 'primary';
      case 'Completed':
        return 'success';
      case 'Delayed':
        return 'warning';
      case 'Cancelled':
        return 'error';
      default:
        return 'default';
    }
  };

  // Get priority color
  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'High':
        return 'error';
      case 'Medium':
        return 'warning';
      case 'Low':
        return 'info';
      default:
        return 'default';
    }
  };

  const handleSearch = (event) => {
    setSearchQuery(event.target.value);
  };

  const handleClearSearch = () => {
    setSearchQuery('');
    fetchTasks();
  };

  const handleServerSearch = async () => {
    if (searchQuery.trim() === '') {
      fetchTasks();
      return;
    }

    try {
      setLoading(true);

      // Try the primary endpoint with search
      try {
        const response = await api.get('/tasks/user/assigned', {
          params: { search: searchQuery }
        });

        console.log('Tasks search response from primary endpoint:', response.data);

        // Check if the response has the expected structure
        if (Array.isArray(response.data)) {
          setTasks(response.data);
          setFilteredTasks(response.data);
          return;
        } else if (response.data && Array.isArray(response.data.tasks)) {
          setTasks(response.data.tasks);
          setFilteredTasks(response.data.tasks);
          return;
        } else {
          console.warn('Unexpected data structure from primary endpoint:', response.data);
        }
      } catch (primaryError) {
        console.warn('Primary endpoint failed for tasks search:', primaryError);
      }

      // If we get here, try the alternative endpoint
      try {
        const altResponse = await api.get('/normaluser/tasks', {
          params: { search: searchQuery }
        });

        console.log('Tasks search response from alternative endpoint:', altResponse.data);

        // Check if the response has the expected structure
        if (Array.isArray(altResponse.data)) {
          setTasks(altResponse.data);
          setFilteredTasks(altResponse.data);
          return;
        } else if (altResponse.data && Array.isArray(altResponse.data.tasks)) {
          setTasks(altResponse.data.tasks);
          setFilteredTasks(altResponse.data.tasks);
          return;
        } else {
          console.warn('Unexpected data structure from alternative endpoint:', altResponse.data);
        }
      } catch (altError) {
        console.error('Alternative endpoint also failed for tasks search:', altError);
      }

      // If server-side search fails, fall back to client-side filtering
      // This is already handled by the useEffect

    } catch (error) {
      console.error('Error searching tasks:', error);
      toast.error('Failed to search tasks');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box className="task-list-container">
      <TaskHeader>
        <Typography variant="h5" component="h2">
          My Tasks
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <TextField
            placeholder="Search tasks..."
            size="small"
            value={searchQuery}
            onChange={handleSearch}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon fontSize="small" color="action" />
                </InputAdornment>
              ),
              endAdornment: searchQuery ? (
                <InputAdornment position="end">
                  <IconButton
                    size="small"
                    onClick={handleClearSearch}
                    aria-label="clear search"
                  >
                    <CloseIcon fontSize="small" />
                  </IconButton>
                </InputAdornment>
              ) : null
            }}
            sx={{ width: 280 }}
          />
          <Tooltip title="Refresh Tasks">
            <IconButton
              onClick={fetchTasks}
              disabled={refreshing}
              color="primary"
            >
              <RefreshIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </TaskHeader>

      {loading ? (
        <Box display="flex" justifyContent="center" my={4}>
          <CircularProgress />
        </Box>
      ) : filteredTasks.length === 0 ? (
        <Typography align="center" sx={{ py: 4 }}>
          {searchQuery ? 'No tasks match your search criteria.' : 'No tasks assigned to you.'}
        </Typography>
      ) : (
        <TableContainer component={Paper} elevation={0}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Title</TableCell>
                <TableCell>Description</TableCell>
                <TableCell>Due Date</TableCell>
                <TableCell>Priority</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Progress</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredTasks.map((task) => (
                <TableRow key={task._id}>
                  <TableCell>{task.title}</TableCell>
                  <TableCell>
                    {task.description.length > 50
                      ? `${task.description.substring(0, 50)}...`
                      : task.description}
                  </TableCell>
                  <TableCell>{formatDate(task.dueDate)}</TableCell>
                  <TableCell>
                    <Chip
                      label={task.priority}
                      color={getPriorityColor(task.priority)}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={task.status}
                      color={getStatusColor(task.status)}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Box sx={{ width: '100%', mr: 1 }}>
                        <Slider
                          value={task.progress || 0}
                          disabled
                          size="small"
                          valueLabelDisplay="auto"
                        />
                      </Box>
                      <Box sx={{ minWidth: 35 }}>
                        <Typography variant="body2" color="text.secondary">
                          {task.progress || 0}%
                        </Typography>
                      </Box>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <Button
                        variant="outlined"
                        size="small"
                        onClick={() => handleOpenDialog('view', task)}
                      >
                        View
                      </Button>
                      {task.status !== 'Completed' && (
                        <>
                          <Button
                            variant="outlined"
                            color="primary"
                            size="small"
                            onClick={() => handleOpenDialog('update', task)}
                          >
                            Update
                          </Button>
                          <Button
                            variant="outlined"
                            color="success"
                            size="small"
                            onClick={() => handleCompleteTask(task._id)}
                          >
                            Complete
                          </Button>
                        </>
                      )}
                    </Box>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      {/* Task Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {dialogType === 'view'
            ? 'Task Details'
            : dialogType === 'update'
            ? 'Update Task'
            : 'Task'}
        </DialogTitle>
        <DialogContent>
          {selectedTask && (
            <Box sx={{ mt: 2 }}>
              {dialogType === 'view' ? (
                // View Task
                <Box>
                  <Typography variant="h6" gutterBottom>
                    {selectedTask.title}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" paragraph>
                    {selectedTask.description}
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mb: 2 }}>
                    <Chip
                      label={`Priority: ${selectedTask.priority}`}
                      color={getPriorityColor(selectedTask.priority)}
                      size="small"
                    />
                    <Chip
                      label={`Status: ${selectedTask.status}`}
                      color={getStatusColor(selectedTask.status)}
                      size="small"
                    />
                    <Chip
                      label={`Due: ${formatDate(selectedTask.dueDate)}`}
                      size="small"
                    />
                  </Box>
                  <Typography variant="subtitle2" gutterBottom>
                    Progress: {selectedTask.progress || 0}%
                  </Typography>
                  <Slider
                    value={selectedTask.progress || 0}
                    disabled
                    valueLabelDisplay="auto"
                    sx={{ mb: 3 }}
                  />
                  {selectedTask.notes && (
                    <>
                      <Typography variant="subtitle2" gutterBottom>
                        Notes:
                      </Typography>
                      <Typography variant="body2" paragraph>
                        {selectedTask.notes}
                      </Typography>
                    </>
                  )}
                  <Box sx={{ mt: 2 }}>
                    <Typography variant="caption" color="text.secondary">
                      Assigned by: {selectedTask.assignedBy?.name || 'Unknown'}
                    </Typography>
                    <br />
                    <Typography variant="caption" color="text.secondary">
                      Created: {formatDate(selectedTask.createdAt)}
                    </Typography>
                    {selectedTask.updatedAt && (
                      <>
                        <br />
                        <Typography variant="caption" color="text.secondary">
                          Last updated: {formatDate(selectedTask.updatedAt)}
                        </Typography>
                      </>
                    )}
                  </Box>
                </Box>
              ) : (
                // Update Task
                <Box>
                  <Typography variant="h6" gutterBottom>
                    {selectedTask.title}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" paragraph>
                    {selectedTask.description}
                  </Typography>
                  <FormControl fullWidth margin="normal">
                    <InputLabel>Status</InputLabel>
                    <Select
                      value={updateData.status}
                      onChange={(e) => setUpdateData({ ...updateData, status: e.target.value })}
                      label="Status"
                    >
                      <MenuItem value="Not Started">Not Started</MenuItem>
                      <MenuItem value="In Progress">In Progress</MenuItem>
                      <MenuItem value="Delayed">Delayed</MenuItem>
                      <MenuItem value="Completed">Completed</MenuItem>
                    </Select>
                  </FormControl>
                  <Box sx={{ mt: 3, mb: 2 }}>
                    <Typography id="progress-slider" gutterBottom>
                      Progress: {updateData.progress}%
                    </Typography>
                    <Slider
                      value={updateData.progress}
                      onChange={(e, newValue) => setUpdateData({ ...updateData, progress: newValue })}
                      aria-labelledby="progress-slider"
                      valueLabelDisplay="auto"
                      step={5}
                      marks
                      min={0}
                      max={100}
                    />
                  </Box>
                  <TextField
                    label="Notes"
                    multiline
                    rows={4}
                    value={updateData.notes}
                    onChange={(e) => setUpdateData({
                      ...updateData,
                      notes: e.target.value,
                      message: e.target.value // Also update message field for API compatibility
                    })}
                    fullWidth
                    margin="normal"
                    placeholder="Add any notes or updates about this task"
                  />
                </Box>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog} color="primary">
            Close
          </Button>
          {dialogType === 'update' && (
            <Button onClick={handleUpdateTask} color="primary" variant="contained">
              Save Changes
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default TaskList;
