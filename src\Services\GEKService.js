import api from './ApiService';

/**
 * GEK (General Estimating Knowledge) Service
 * Handles all GEK-related API calls for employee performance analysis and task assignment
 */
class GEKService {
  /**
   * Get employee recommendations for a specific task - Shows ALL users for HR assignment
   * @param {string} taskId - The task ID to get recommendations for
   * @returns {Promise} API response with ranked employees
   */
  static async getTaskEmployeeMatches(taskId) {
    try {
      // First try to get real data from backend
      const response = await api.get(`/api/gek/task-employee-match/${taskId}`);
      return response.data;
    } catch (error) {
      console.error('Error getting task employee matches from API:', error);

      // Fallback: Get real users and generate matches
      try {
        const usersResponse = await api.get('/api/hr/users');
        const users = usersResponse.data.users || usersResponse.data;

        // Filter to get only normal users (not HR or admin)
        const employees = users.filter(user => user.role === 'user');

        if (employees.length === 0) {
          return {
            taskId,
            rankedEmployees: [],
            totalCandidates: 0,
            analysisTimestamp: new Date().toISOString(),
            message: 'No employees found. Please ensure users are registered in the system.',
            showAllUsers: true
          };
        }

        return this.generateRealEmployeeMatches(taskId, employees);
      } catch (userError) {
        console.error('Error getting users for matches:', userError);
        // Final fallback: Return empty but structured response
        return {
          taskId,
          rankedEmployees: [],
          totalCandidates: 0,
          analysisTimestamp: new Date().toISOString(),
          error: userError.message,
          message: 'Failed to load employees. Please try again.',
          showAllUsers: true
        };
      }
    }
  }

  /**
   * Reassign a task to a different employee
   * @param {string} taskId - The task ID
   * @param {string} employeeId - The employee ID to assign to
   * @param {string} reason - Reason for assignment
   * @returns {Promise} API response
   */
  static async reassignTask(taskId, employeeId, reason = '') {
    try {
      const response = await api.put(`/api/tasks/${taskId}`, {
        assignedTo: employeeId,
        status: 'Assigned',
        assignmentReason: reason
      });
      return response.data;
    } catch (error) {
      console.error('Error reassigning task:', error);
      throw error;
    }
  }

  /**
   * Calculate fit score for an employee and task combination
   * @param {string} employeeId - The employee ID
   * @param {string} category - Task category
   * @param {string} priority - Task priority
   * @returns {Promise} Fit score data
   */
  static async calculateFitScore(employeeId, category, priority) {
    try {
      const response = await api.post('/api/gek/calculate-fit', {
        employeeId,
        category,
        priority
      });
      return response.data;
    } catch (error) {
      console.error('Error calculating fit score:', error);
      
      // Fallback: Return mock fit score
      return this.getMockFitScore(employeeId, category, priority);
    }
  }

  /**
   * Get GEK analytics and insights using real data
   * @returns {Promise} GEK analytics data
   */
  static async getGEKAnalytics() {
    try {
      // Try to get real analytics from backend
      const response = await api.get('/api/gek/analytics');
      return response.data;
    } catch (error) {
      console.error('Error getting GEK analytics from API:', error);

      // Generate analytics from real data
      try {
        return await this.generateRealAnalytics();
      } catch (realDataError) {
        console.error('Error generating real analytics:', realDataError);
        // Final fallback: Return basic analytics
        return this.getBasicAnalytics();
      }
    }
  }

  /**
   * Get employee performance metrics using real data
   * @param {string} employeeId - The employee ID
   * @returns {Promise} Employee performance data
   */
  static async getEmployeePerformance(employeeId) {
    try {
      // Try to get real performance data from backend
      const response = await api.get(`/api/gek/employee/${employeeId}/performance`);
      return response.data;
    } catch (error) {
      console.error('Error getting employee performance from API:', error);

      // Generate performance data from real task data
      try {
        return await this.generateRealEmployeePerformance(employeeId);
      } catch (realDataError) {
        console.error('Error generating real employee performance:', realDataError);
        // Final fallback: Return basic performance data
        return this.getBasicEmployeePerformance(employeeId);
      }
    }
  }

  /**
   * Generate employee performance from real task data
   * @param {string} employeeId - Employee ID
   * @returns {Promise} Real performance data
   */
  static async generateRealEmployeePerformance(employeeId) {
    try {
      // Get employee's tasks
      const tasksResponse = await api.get(`/api/tasks/hr?assignedTo=${employeeId}&limit=100`);
      const tasks = tasksResponse.data.tasks || tasksResponse.data || [];

      // Get employee details
      const userResponse = await api.get(`/api/hr/users`);
      const allUsers = userResponse.data.users || userResponse.data || [];
      const user = allUsers.find(u => u._id === employeeId);

      const completedTasks = tasks.filter(task => task.status === 'Completed');
      const tasksWithTime = completedTasks.filter(task => task.actualCompletionTime > 0);
      const tasksWithFitScore = tasks.filter(task => task.gekFitScore > 0);

      // Calculate metrics
      const tasksCompleted = completedTasks.length;
      const averageCompletionTime = tasksWithTime.length > 0
        ? tasksWithTime.reduce((sum, task) => sum + task.actualCompletionTime, 0) / tasksWithTime.length
        : 0;

      const averageFitScore = tasksWithFitScore.length > 0
        ? tasksWithFitScore.reduce((sum, task) => sum + task.gekFitScore, 0) / tasksWithFitScore.length
        : 75;

      // Calculate on-time delivery
      const onTimeDelivery = completedTasks.length > 0
        ? completedTasks.filter(task => {
            const deadline = new Date(task.deadline);
            const completed = new Date(task.completedAt || task.updatedAt);
            return completed <= deadline;
          }).length / completedTasks.length
        : 0.85;

      // Quality rating based on fit scores and completion rate
      const qualityRating = Math.min(5, (averageFitScore / 20) + (onTimeDelivery * 2));

      return {
        employeeId,
        employeeName: user.name,
        overallScore: Math.round(averageFitScore),
        tasksCompleted,
        averageCompletionTime: Math.round(averageCompletionTime * 10) / 10,
        onTimeDelivery: Math.round(onTimeDelivery * 100) / 100,
        qualityRating: Math.round(qualityRating * 10) / 10,
        skillAreas: [
          { skill: 'Task Completion', score: Math.min(100, tasksCompleted * 5) },
          { skill: 'Time Management', score: Math.round((1 / Math.max(0.1, averageCompletionTime / 8)) * 100) },
          { skill: 'Quality Delivery', score: Math.round(qualityRating * 20) },
          { skill: 'Reliability', score: Math.round(onTimeDelivery * 100) }
        ],
        recentTasks: completedTasks.slice(-3).map(task => ({
          title: task.title,
          score: task.gekFitScore || 75,
          completedIn: task.actualCompletionTime || 8,
          completedAt: task.completedAt
        })),
        lastUpdated: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error generating real employee performance:', error);
      throw error;
    }
  }

  /**
   * Get basic employee performance when real data is not available
   * @param {string} employeeId - Employee ID
   * @returns {Object} Basic performance data
   */
  static getBasicEmployeePerformance(employeeId) {
    return {
      employeeId,
      employeeName: 'Unknown Employee',
      overallScore: 0,
      tasksCompleted: 0,
      averageCompletionTime: 0,
      onTimeDelivery: 0,
      qualityRating: 0,
      skillAreas: [
        { skill: 'Task Completion', score: 0 },
        { skill: 'Time Management', score: 0 },
        { skill: 'Quality Delivery', score: 0 },
        { skill: 'Reliability', score: 0 }
      ],
      recentTasks: [],
      lastUpdated: new Date().toISOString(),
      message: 'No performance data available yet. Complete some tasks to see metrics.'
    };
  }

  /**
   * Get ranked employees for task assignment - Shows ALL users for HR assignment
   * @param {Object} criteria - Task criteria (category, priority)
   * @param {Array} userIds - Specific user IDs (optional)
   * @param {number} limit - Number of results to return (default: all users)
   * @returns {Promise} Ranked employees data
   */
  static async getRankedEmployees(criteria, userIds = null, limit = null) {
    try {
      // Try to get from backend first
      const params = new URLSearchParams();
      if (criteria.category) params.append('category', criteria.category);
      if (criteria.priority) params.append('priority', criteria.priority);
      if (criteria.department) params.append('department', criteria.department);
      if (limit) params.append('limit', limit.toString());

      const response = await api.get(`/api/gek/ranked-employees?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Error getting ranked employees from backend:', error);

      // Fallback: Generate from frontend
      try {
        // Get all users (excluding HR and admin roles)
        const usersResponse = await api.get('/api/hr/users');
        const allUsers = (usersResponse.data.users || usersResponse.data || [])
          .filter(user => user.role === 'user'); // Only normal users for task assignment

        // Filter by specific user IDs if provided
        const users = userIds ? allUsers.filter(user => userIds.includes(user._id)) : allUsers;

        // Always show users even if none found - HR needs to see all employees
        if (users.length === 0) {
          return {
            rankedEmployees: [],
            totalCandidates: 0,
            criteria,
            message: 'No employees found. Please ensure users are registered in the system.',
            showAllUsers: true
          };
        }

        // Create a mock task for scoring
        const mockTask = {
          category: criteria.category || 'General',
          priority: criteria.priority || 'Medium',
          title: `${criteria.category} Task`,
          description: `Sample ${criteria.category} task for ranking`
        };

        // Generate fit scores for ALL employees (no filtering by score)
        const rankedEmployees = users.map(user => {
          const fitScore = this.calculateEmployeeFitScore(user, mockTask);
          const estimatedTime = this.estimateCompletionTime(user, mockTask);

          return {
            user: {
              _id: user._id,
              name: user.name,
              email: user.email,
              job: user.job || 'Employee',
              department: user.department || 'Not Assigned'
            },
            fitScore,
            estimatedCompletionTime: estimatedTime,
            confidence: fitScore / 100,
            reasons: this.generateFitReasons(user, mockTask, fitScore)
          };
        });

        // Sort by fit score (highest first) but show ALL users
        rankedEmployees.sort((a, b) => b.fitScore - a.fitScore);

        // Apply limit only if specified, otherwise show all users
        const finalEmployees = limit ? rankedEmployees.slice(0, limit) : rankedEmployees;

        return {
          rankedEmployees: finalEmployees,
          totalCandidates: users.length,
          criteria,
          analysisTimestamp: new Date().toISOString(),
          showAllUsers: true
        };
      } catch (fallbackError) {
        console.error('Error in fallback ranked employees:', fallbackError);

        // Return empty result but indicate error
        return {
          rankedEmployees: [],
          totalCandidates: 0,
          criteria,
          error: fallbackError.message,
          message: 'Failed to load employees. Please try again.',
          showAllUsers: true
        };
      }
    }
  }

  /**
   * Get department ranking for specific department - Shows ALL users in department
   * @param {string} department - Department name
   * @param {string} category - Task category
   * @param {string} priority - Task priority
   * @param {number} limit - Number of results (default: all users in department)
   * @returns {Promise} Department ranking data
   */
  static async getDepartmentRanking(department, category, priority, limit = null) {
    try {
      // Try to get from backend first
      const params = new URLSearchParams();
      params.append('department', department);
      params.append('category', category);
      params.append('priority', priority);
      if (limit) params.append('limit', limit.toString());

      const response = await api.get(`/api/gek/department-ranking?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Error getting department ranking from backend:', error);

      // Fallback: Use getRankedEmployees with department filter
      return await this.getRankedEmployees({ category, priority, department }, null, limit);
    }
  }

  /**
   * Get user estimates for all categories
   * @param {string} userId - User ID
   * @returns {Promise} User estimates data
   */
  static async getUserEstimates(userId) {
    try {
      // Try to get from backend first
      const response = await api.get(`/api/gek/user/${userId}/estimates`);
      return response.data;
    } catch (error) {
      console.error('Error getting user estimates from API:', error);

      // Generate estimates for all categories
      try {
        return await this.generateUserEstimates(userId);
      } catch (generateError) {
        console.error('Error generating user estimates:', generateError);
        return { estimates: [] };
      }
    }
  }

  /**
   * Generate user estimates for all task categories
   * @param {string} userId - User ID
   * @returns {Promise} Generated estimates
   */
  static async generateUserEstimates(userId) {
    try {
      const usersResponse = await api.get('/api/hr/users');
      const allUsers = usersResponse.data.users || usersResponse.data || [];
      const user = allUsers.find(u => u._id === userId);

      const categories = ['General', 'Development', 'Design', 'Marketing', 'HR', 'Finance', 'Operations'];
      const priorities = ['Low', 'Medium', 'High', 'Urgent'];

      const estimates = [];

      for (const category of categories) {
        for (const priority of priorities) {
          const mockTask = {
            category,
            priority,
            title: `${category} Task`,
            description: `Sample ${category} task`
          };

          const fitScore = this.calculateEmployeeFitScore(user, mockTask);
          const estimatedTime = this.estimateCompletionTime(user, mockTask);

          estimates.push({
            taskCategory: category,
            taskPriority: priority,
            fitScore,
            estimatedCompletionTime: estimatedTime,
            confidence: fitScore / 100,
            performanceMetrics: {
              completionRate: Math.min(1, fitScore / 100),
              averageTime: estimatedTime,
              qualityScore: fitScore / 100
            }
          });
        }
      }

      return {
        userId,
        estimates,
        lastUpdated: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error generating user estimates:', error);
      throw error;
    }
  }

  /**
   * Get fit score for specific user and task criteria
   * @param {string} userId - User ID
   * @param {string} category - Task category
   * @param {string} priority - Task priority
   * @param {boolean} forceRecalculation - Force recalculation
   * @returns {Promise} Fit score data
   */
  static async getFitScore(userId, category, priority, forceRecalculation = false) {
    try {
      // Try to get from backend first
      if (!forceRecalculation) {
        const response = await api.get(`/api/gek/fit-score/${userId}?category=${category}&priority=${priority}`);
        return response.data;
      }
    } catch (error) {
      console.log('Backend fit score not available, generating...');
    }

    // Generate fit score
    try {
      const usersResponse = await api.get('/api/hr/users');
      const allUsers = usersResponse.data.users || usersResponse.data || [];
      const user = allUsers.find(u => u._id === userId);

      const mockTask = {
        category,
        priority,
        title: `${category} Task`,
        description: `Sample ${category} task`
      };

      const fitScore = this.calculateEmployeeFitScore(user, mockTask);
      const estimatedTime = this.estimateCompletionTime(user, mockTask);

      return {
        estimate: {
          taskCategory: category,
          taskPriority: priority,
          fitScore,
          estimatedCompletionTime: estimatedTime,
          confidence: fitScore / 100,
          performanceMetrics: {
            completionRate: Math.min(1, fitScore / 100),
            averageTime: estimatedTime,
            qualityScore: fitScore / 100
          }
        }
      };
    } catch (error) {
      console.error('Error calculating fit score:', error);
      throw error;
    }
  }

  /**
   * Generate employee matches using real user data
   * @param {string} taskId - Task ID
   * @param {Array} employees - Array of real employee data
   * @returns {Object} Employee matches with real data
   */
  static async generateRealEmployeeMatches(taskId, employees) {
    try {
      // Get task details to analyze
      const taskResponse = await api.get(`/api/tasks/${taskId}`);
      const task = taskResponse.data;

      // Generate fit scores for each employee
      const rankedEmployees = employees.map(employee => {
        const fitScore = this.calculateEmployeeFitScore(employee, task);
        const estimatedTime = this.estimateCompletionTime(employee, task);

        return {
          user: {
            _id: employee._id,
            name: employee.name,
            email: employee.email,
            job: employee.job || 'Employee',
            department: employee.department || 'General'
          },
          fitScore,
          estimatedCompletionTime: estimatedTime,
          confidence: fitScore / 100,
          reasons: this.generateFitReasons(employee, task, fitScore)
        };
      });

      // Sort by fit score (highest first)
      rankedEmployees.sort((a, b) => b.fitScore - a.fitScore);

      return {
        taskId,
        rankedEmployees,
        totalCandidates: rankedEmployees.length,
        analysisTimestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error generating real employee matches:', error);
      return this.getMockEmployeeMatches(taskId);
    }
  }

  /**
   * Calculate fit score for an employee and task - Ensures all users get reasonable scores
   * @param {Object} employee - Employee data
   * @param {Object} task - Task data
   * @returns {number} Fit score (40-95) - Always shows users with minimum viable score
   */
  static calculateEmployeeFitScore(employee, task) {
    let score = 50; // Higher base score so all users are viable

    // Department match bonus
    const departmentMap = {
      'Development': ['Engineering', 'IT', 'Technology', 'Software', 'Tech'],
      'Design': ['Design', 'Creative', 'UX', 'UI', 'Graphics'],
      'Marketing': ['Marketing', 'Sales', 'Communications', 'Advertising'],
      'HR': ['HR', 'Human Resources', 'People', 'Recruitment'],
      'Finance': ['Finance', 'Accounting', 'Financial', 'Budget'],
      'Operations': ['Operations', 'Management', 'Admin', 'Business'],
      'General': ['General', 'Support', 'Assistant', 'Coordinator']
    };

    const taskCategory = task.category || 'General';
    const employeeDept = employee.department || '';
    const relevantDepts = departmentMap[taskCategory] || [];

    // Department matching
    if (relevantDepts.some(dept => employeeDept.toLowerCase().includes(dept.toLowerCase()))) {
      score += 15;
    } else if (employeeDept) {
      // Even if not perfect match, give some credit for having a department
      score += 5;
    }

    // Job title relevance
    const jobTitle = (employee.job || '').toLowerCase();

    // Specific job title matches
    if (taskCategory === 'Development' && (jobTitle.includes('developer') || jobTitle.includes('engineer') || jobTitle.includes('programmer'))) {
      score += 12;
    } else if (taskCategory === 'Design' && (jobTitle.includes('designer') || jobTitle.includes('ux') || jobTitle.includes('ui'))) {
      score += 12;
    } else if (taskCategory === 'Marketing' && (jobTitle.includes('marketing') || jobTitle.includes('sales') || jobTitle.includes('communication'))) {
      score += 12;
    } else if (taskCategory === 'HR' && (jobTitle.includes('hr') || jobTitle.includes('human') || jobTitle.includes('recruitment'))) {
      score += 12;
    } else if (taskCategory === 'Finance' && (jobTitle.includes('finance') || jobTitle.includes('accounting') || jobTitle.includes('budget'))) {
      score += 12;
    } else if (taskCategory === 'Operations' && (jobTitle.includes('operations') || jobTitle.includes('manager') || jobTitle.includes('admin'))) {
      score += 12;
    } else if (jobTitle) {
      // Give some credit for having any job title
      score += 3;
    }

    // Seniority adjustments
    if (jobTitle.includes('senior') || jobTitle.includes('lead') || jobTitle.includes('manager')) {
      score += 8;
      // Senior people are better for urgent tasks
      if (task.priority === 'Urgent') {
        score += 5;
      }
    } else if (jobTitle.includes('junior') || jobTitle.includes('intern')) {
      // Junior people are good for low priority tasks
      if (task.priority === 'Low') {
        score += 5;
      }
    }

    // Priority-based adjustments
    if (task.priority === 'Urgent') {
      // Urgent tasks need experienced people
      score += 3;
    } else if (task.priority === 'Low') {
      // Low priority tasks can be done by anyone
      score += 2;
    }

    // Add some variation to make it realistic (but smaller range)
    const randomFactor = Math.random() * 10 - 5; // -5 to +5
    score += randomFactor;

    // Ensure score is always in a reasonable range (40-95)
    // This ensures ALL users appear as viable options for HR
    return Math.max(40, Math.min(95, Math.round(score)));
  }

  /**
   * Estimate completion time for employee and task
   * @param {Object} employee - Employee data
   * @param {Object} task - Task data
   * @returns {number} Estimated hours
   */
  static estimateCompletionTime(employee, task) {
    const baseHours = {
      'Development': 16,
      'Design': 12,
      'Marketing': 8,
      'HR': 6,
      'Finance': 10,
      'Operations': 8,
      'General': 6
    };

    let hours = baseHours[task.category] || baseHours['General'];

    // Adjust based on priority
    if (task.priority === 'Urgent') hours *= 0.7;
    if (task.priority === 'Low') hours *= 1.3;

    // Adjust based on employee seniority
    const jobTitle = (employee.job || '').toLowerCase();
    if (jobTitle.includes('senior') || jobTitle.includes('lead')) {
      hours *= 0.8;
    }
    if (jobTitle.includes('junior')) {
      hours *= 1.2;
    }

    return Math.max(2, Math.round(hours));
  }

  /**
   * Generate reasons for fit score
   * @param {Object} employee - Employee data
   * @param {Object} task - Task data
   * @param {number} fitScore - Calculated fit score
   * @returns {Array} Array of reasons
   */
  static generateFitReasons(employee, task, fitScore) {
    const reasons = [];

    if (fitScore >= 80) {
      reasons.push('Excellent match for task requirements');
    }
    if (fitScore >= 70) {
      reasons.push('Strong relevant experience');
    }

    const jobTitle = (employee.job || '').toLowerCase();
    if (jobTitle.includes('senior')) {
      reasons.push('Senior-level expertise');
    }
    if (jobTitle.includes('lead')) {
      reasons.push('Leadership experience');
    }

    if (task.priority === 'Urgent' && fitScore >= 75) {
      reasons.push('Suitable for urgent tasks');
    }

    if (employee.department && task.category) {
      reasons.push(`${employee.department} department experience`);
    }

    if (reasons.length === 0) {
      reasons.push('Available for assignment');
    }

    return reasons.slice(0, 3); // Limit to 3 reasons
  }

  /**
   * Generate analytics from real data
   * @returns {Promise} Real analytics data
   */
  static async generateRealAnalytics() {
    try {
      // Get real users and tasks data
      const [usersResponse, tasksResponse] = await Promise.all([
        api.get('/api/hr/users'),
        api.get('/api/tasks/hr?limit=1000') // Get more tasks for better analytics
      ]);

      const users = (usersResponse.data.users || usersResponse.data || []).filter(user => user.role !== 'hr');
      const tasks = tasksResponse.data.tasks || tasksResponse.data || [];

      // Calculate analytics from real data
      const totalTasks = tasks.length;
      const completedTasks = tasks.filter(task => task.status === 'Completed');
      const assignedTasks = tasks.filter(task => task.assignedTo);

      // Calculate average fit score from assigned tasks
      const tasksWithFitScore = assignedTasks.filter(task => task.gekFitScore > 0);
      const averageFitScore = tasksWithFitScore.length > 0
        ? tasksWithFitScore.reduce((sum, task) => sum + task.gekFitScore, 0) / tasksWithFitScore.length
        : 75; // Default if no fit scores

      // Calculate average completion time
      const tasksWithTime = completedTasks.filter(task => task.actualCompletionTime > 0);
      const averageCompletionTime = tasksWithTime.length > 0
        ? tasksWithTime.reduce((sum, task) => sum + task.actualCompletionTime, 0) / tasksWithTime.length
        : 8; // Default 8 hours

      // Calculate success rate (completed vs assigned)
      const successRate = assignedTasks.length > 0
        ? completedTasks.length / assignedTasks.length
        : 0.85; // Default 85%

      // Get top performers (users with most completed tasks)
      const userTaskCounts = {};
      completedTasks.forEach(task => {
        if (task.assignedTo) {
          const userId = task.assignedTo._id || task.assignedTo;
          userTaskCounts[userId] = (userTaskCounts[userId] || 0) + 1;
        }
      });

      const topPerformers = Object.entries(userTaskCounts)
        .map(([userId, taskCount]) => {
          const user = users.find(u => u._id === userId);
          return user ? {
            name: user.name,
            score: Math.min(95, 70 + taskCount * 2), // Estimate score based on task count
            tasksCompleted: taskCount
          } : null;
        })
        .filter(Boolean)
        .sort((a, b) => b.tasksCompleted - a.tasksCompleted)
        .slice(0, 5);

      // Calculate category performance
      const categoryPerformance = {};
      const categories = ['Development', 'Design', 'Marketing', 'HR', 'Finance', 'Operations', 'General'];

      categories.forEach(category => {
        const categoryTasks = tasks.filter(task => task.category === category);
        const categoryCompleted = categoryTasks.filter(task => task.status === 'Completed');
        const categoryWithFitScore = categoryTasks.filter(task => task.gekFitScore > 0);
        const categoryWithTime = categoryCompleted.filter(task => task.actualCompletionTime > 0);

        categoryPerformance[category] = {
          avgScore: categoryWithFitScore.length > 0
            ? categoryWithFitScore.reduce((sum, task) => sum + task.gekFitScore, 0) / categoryWithFitScore.length
            : 75,
          avgTime: categoryWithTime.length > 0
            ? categoryWithTime.reduce((sum, task) => sum + task.actualCompletionTime, 0) / categoryWithTime.length
            : 8
        };
      });

      return {
        totalTasks,
        totalUsers: users.length,
        averageFitScore: Math.round(averageFitScore * 10) / 10,
        averageCompletionTime: Math.round(averageCompletionTime * 10) / 10,
        successRate: Math.round(successRate * 100) / 100,
        topPerformers,
        categoryPerformance,
        trends: {
          fitScoreImprovement: 0.05, // Placeholder - would need historical data
          timeReduction: 0.03,
          accuracyIncrease: 0.08
        },
        lastUpdated: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error generating real analytics:', error);
      throw error;
    }
  }

  /**
   * Get basic analytics when real data is not available
   * @returns {Object} Basic analytics
   */
  static getBasicAnalytics() {
    return {
      totalTasks: 0,
      totalUsers: 0,
      averageFitScore: 0,
      averageCompletionTime: 0,
      successRate: 0,
      topPerformers: [],
      categoryPerformance: {},
      trends: {
        fitScoreImprovement: 0,
        timeReduction: 0,
        accuracyIncrease: 0
      },
      lastUpdated: new Date().toISOString(),
      message: 'No data available yet. Create some tasks and assignments to see analytics.'
    };
  }

  // Mock data methods for development/fallback (keeping minimal for emergency use)

  /**
   * Fallback method when no real users are available
   * @param {string} taskId - Task ID
   * @returns {Object} Empty employee matches
   */
  static getMockEmployeeMatches(taskId) {
    return {
      taskId,
      rankedEmployees: [],
      totalCandidates: 0,
      analysisTimestamp: new Date().toISOString(),
      message: 'No employees found. Please ensure users are registered in the system.'
    };
  }

  /**
   * Generate mock fit score
   * @param {string} employeeId - Employee ID
   * @param {string} category - Task category
   * @param {string} priority - Task priority
   * @returns {Object} Mock fit score data
   */
  static getMockFitScore(employeeId, category, priority) {
    // Simple algorithm to generate consistent but varied scores
    const baseScore = 60 + (parseInt(employeeId.slice(-1)) || 1) * 5;
    const categoryBonus = category === 'Development' ? 10 : category === 'Design' ? 8 : 5;
    const priorityBonus = priority === 'High' ? 5 : priority === 'Urgent' ? 8 : 2;
    
    const fitScore = Math.min(95, baseScore + categoryBonus + priorityBonus);
    const estimatedCompletionTime = Math.max(4, 16 - (fitScore / 10));

    return {
      fitScore,
      estimatedCompletionTime: Math.round(estimatedCompletionTime),
      confidence: fitScore / 100,
      factors: {
        skillMatch: fitScore * 0.4,
        experienceLevel: fitScore * 0.3,
        availability: fitScore * 0.2,
        performanceHistory: fitScore * 0.1
      }
    };
  }

  /**
   * Generate mock analytics data
   * @returns {Object} Mock analytics
   */
  static getMockAnalytics() {
    return {
      totalTasks: 156,
      averageFitScore: 78.5,
      averageCompletionTime: 9.2,
      successRate: 0.89,
      topPerformers: [
        { name: 'John Smith', score: 92, tasksCompleted: 23 },
        { name: 'Sarah Johnson', score: 87, tasksCompleted: 19 },
        { name: 'Mike Chen', score: 82, tasksCompleted: 21 }
      ],
      categoryPerformance: {
        'Development': { avgScore: 85, avgTime: 8.5 },
        'Design': { avgScore: 82, avgTime: 10.2 },
        'Marketing': { avgScore: 78, avgTime: 7.8 },
        'Operations': { avgScore: 75, avgTime: 9.5 }
      },
      trends: {
        fitScoreImprovement: 0.12,
        timeReduction: 0.08,
        accuracyIncrease: 0.15
      }
    };
  }

  /**
   * Generate mock employee performance data
   * @param {string} employeeId - Employee ID
   * @returns {Object} Mock performance data
   */
  static getMockEmployeePerformance(employeeId) {
    const basePerformance = 70 + (parseInt(employeeId.slice(-1)) || 1) * 3;
    
    return {
      employeeId,
      overallScore: basePerformance,
      tasksCompleted: 15 + Math.floor(Math.random() * 20),
      averageCompletionTime: 8 + Math.random() * 4,
      onTimeDelivery: 0.85 + Math.random() * 0.1,
      qualityRating: 4.2 + Math.random() * 0.6,
      skillAreas: [
        { skill: 'Technical Skills', score: basePerformance + 5 },
        { skill: 'Communication', score: basePerformance - 2 },
        { skill: 'Problem Solving', score: basePerformance + 3 },
        { skill: 'Time Management', score: basePerformance }
      ],
      recentTasks: [
        { title: 'Feature Implementation', score: basePerformance + 8, completedIn: 6 },
        { title: 'Bug Fixes', score: basePerformance + 5, completedIn: 4 },
        { title: 'Code Review', score: basePerformance + 2, completedIn: 2 }
      ]
    };
  }
}

export default GEKService;
