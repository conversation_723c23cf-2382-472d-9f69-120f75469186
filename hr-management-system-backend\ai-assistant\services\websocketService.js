/**
 * WebSocket Service for Real-Time Jarvis Communication
 * Handles real-time messaging, typing indicators, and live updates
 */

const WebSocket = require('ws');
const jwt = require('jsonwebtoken');
const realTimeEngine = require('./realTimeEngine');

class WebSocketService {
  constructor() {
    this.wss = null;
    this.clients = new Map(); // userId -> WebSocket connection
    this.typingUsers = new Map(); // userId -> typing status
    this.activeConversations = new Map(); // conversationId -> Set of userIds

    this.initializeWebSocketServer();
  }

  /**
   * Initialize WebSocket server
   */
  initializeWebSocketServer() {
    // This will be initialized when the HTTP server is created
    console.log('🔌 WebSocket service initialized - waiting for server attachment');
  }

  /**
   * Attach WebSocket server to HTTP server
   */
  attachToServer(server) {
    this.wss = new WebSocket.Server({
      server,
      path: '/ws/chat',
      verifyClient: this.verifyClient.bind(this)
    });

    this.wss.on('connection', this.handleConnection.bind(this));
    console.log('🔌 WebSocket server attached and ready for real-time communication');
  }

  /**
   * Verify client connection with JWT authentication
   */
  verifyClient(info) {
    try {
      const url = new URL(info.req.url, 'http://localhost');
      const token = url.searchParams.get('token');

      if (!token) {
        console.log('❌ WebSocket connection rejected: No token provided');
        return false;
      }

      const decoded = jwt.verify(token, process.env.JWT_SECRET || 'yourSecretKey');
      info.req.user = decoded;
      return true;
    } catch (error) {
      console.log('❌ WebSocket connection rejected: Invalid token');
      return false;
    }
  }

  /**
   * Handle new WebSocket connection
   */
  handleConnection(ws, req) {
    const userId = req.user.id;
    const userName = req.user.name || 'User';

    console.log(`🔌 Jarvis WebSocket connected: ${userName} (${userId})`);

    // Store connection
    this.clients.set(userId, ws);

    // Send welcome message
    this.sendToUser(userId, {
      type: 'system',
      content: `🤖 Jarvis AI System Online - Real-time communication established. All systems operational, ${userName}. Powered by GPT-4.1 for enhanced intelligence.`,
      timestamp: new Date().toISOString(),
      metadata: {
        connectionStatus: 'established',
        realTimeFeatures: true,
        assistantName: 'Jarvis',
        modelVersion: 'GPT-4.1',
        powered_by: 'OpenAI GPT-4.1'
      }
    });

    // Set up message handlers
    ws.on('message', (data) => this.handleMessage(userId, data));
    ws.on('close', () => this.handleDisconnection(userId));
    ws.on('error', (error) => this.handleError(userId, error));

    // Send typing indicator setup
    this.sendToUser(userId, {
      type: 'config',
      typingIndicator: true,
      realTimeProcessing: true,
      streamingResponses: process.env.AI_REAL_TIME_PROCESSING === 'true'
    });
  }

  /**
   * Handle incoming WebSocket message
   */
  async handleMessage(userId, data) {
    try {
      const message = JSON.parse(data.toString());

      switch (message.type) {
        case 'chat_message':
          await this.handleChatMessage(userId, message);
          break;

        case 'typing_start':
          this.handleTypingStart(userId, message.conversationId);
          break;

        case 'typing_stop':
          this.handleTypingStop(userId, message.conversationId);
          break;

        case 'ping':
          this.sendToUser(userId, { type: 'pong', timestamp: new Date().toISOString() });
          break;

        default:
          console.log(`❓ Unknown message type: ${message.type}`);
      }
    } catch (error) {
      console.error('Error handling WebSocket message:', error);
      this.sendToUser(userId, {
        type: 'error',
        content: 'Message processing error',
        error: error.message
      });
    }
  }

  /**
   * Handle chat message with real-time processing
   */
  async handleChatMessage(userId, message) {
    const { content, conversationId, context = {} } = message;

    // Send typing indicator for Jarvis
    this.sendToUser(userId, {
      type: 'assistant_typing',
      assistantName: 'Jarvis',
      status: 'analyzing'
    });

    try {
      // Process with real-time engine
      const response = await realTimeEngine.processMessage(userId, content, {
        ...context,
        conversationId,
        realTime: true,
        websocket: true
      });

      // Stop typing indicator
      this.sendToUser(userId, {
        type: 'assistant_typing',
        assistantName: 'Jarvis',
        status: 'stopped'
      });

      // Send response
      this.sendToUser(userId, {
        type: 'chat_response',
        content: response.content,
        conversationId,
        metadata: {
          ...response.metadata,
          responseTime: response.responseTime,
          realTimeProcessed: true,
          assistantName: 'Jarvis',
          model: 'jarvis-v2.0',
          modelVersion: 'GPT-4.1',
          powered_by: 'OpenAI GPT-4.1'
        },
        suggestions: response.suggestions || [],
        timestamp: new Date().toISOString()
      });

      // Update conversation participants
      this.updateConversationParticipants(conversationId, userId);

    } catch (error) {
      console.error('Error processing chat message:', error);

      // Stop typing indicator
      this.sendToUser(userId, {
        type: 'assistant_typing',
        assistantName: 'Jarvis',
        status: 'stopped'
      });

      // Send error response
      this.sendToUser(userId, {
        type: 'chat_response',
        content: 'I apologize, but I encountered a processing error. My systems are self-correcting. Please try again.',
        conversationId,
        error: true,
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Handle typing start
   */
  handleTypingStart(userId, conversationId) {
    this.typingUsers.set(userId, {
      conversationId,
      timestamp: Date.now()
    });

    // Notify other participants in the conversation
    this.broadcastToConversation(conversationId, userId, {
      type: 'user_typing',
      userId,
      status: 'typing'
    });
  }

  /**
   * Handle typing stop
   */
  handleTypingStop(userId, conversationId) {
    this.typingUsers.delete(userId);

    // Notify other participants
    this.broadcastToConversation(conversationId, userId, {
      type: 'user_typing',
      userId,
      status: 'stopped'
    });
  }

  /**
   * Handle client disconnection
   */
  handleDisconnection(userId) {
    console.log(`🔌 Jarvis WebSocket disconnected: ${userId}`);

    // Clean up
    this.clients.delete(userId);
    this.typingUsers.delete(userId);

    // Remove from active conversations
    for (const [conversationId, participants] of this.activeConversations.entries()) {
      participants.delete(userId);
      if (participants.size === 0) {
        this.activeConversations.delete(conversationId);
      }
    }
  }

  /**
   * Handle WebSocket error
   */
  handleError(userId, error) {
    console.error(`WebSocket error for user ${userId}:`, error);
  }

  /**
   * Send message to specific user
   */
  sendToUser(userId, message) {
    const client = this.clients.get(userId);
    if (client && client.readyState === WebSocket.OPEN) {
      try {
        client.send(JSON.stringify(message));
        return true;
      } catch (error) {
        console.error(`Error sending message to user ${userId}:`, error);
        return false;
      }
    }
    return false;
  }

  /**
   * Broadcast message to all participants in a conversation
   */
  broadcastToConversation(conversationId, excludeUserId, message) {
    const participants = this.activeConversations.get(conversationId);
    if (participants) {
      participants.forEach(userId => {
        if (userId !== excludeUserId) {
          this.sendToUser(userId, message);
        }
      });
    }
  }

  /**
   * Update conversation participants
   */
  updateConversationParticipants(conversationId, userId) {
    if (!this.activeConversations.has(conversationId)) {
      this.activeConversations.set(conversationId, new Set());
    }
    this.activeConversations.get(conversationId).add(userId);
  }

  /**
   * Send system notification to user
   */
  sendSystemNotification(userId, notification) {
    this.sendToUser(userId, {
      type: 'system_notification',
      ...notification,
      timestamp: new Date().toISOString(),
      from: 'Jarvis'
    });
  }

  /**
   * Send real-time status update
   */
  sendStatusUpdate(userId, status) {
    this.sendToUser(userId, {
      type: 'status_update',
      status,
      timestamp: new Date().toISOString(),
      assistantName: 'Jarvis'
    });
  }

  /**
   * Get connection statistics
   */
  getStats() {
    return {
      connectedClients: this.clients.size,
      activeConversations: this.activeConversations.size,
      typingUsers: this.typingUsers.size,
      serverStatus: this.wss ? 'running' : 'not_initialized'
    };
  }

  /**
   * Check if user is connected
   */
  isUserConnected(userId) {
    const client = this.clients.get(userId);
    return client && client.readyState === WebSocket.OPEN;
  }

  /**
   * Clean up typing indicators (called periodically)
   */
  cleanupTypingIndicators() {
    const now = Date.now();
    const timeout = 10000; // 10 seconds

    for (const [userId, typingInfo] of this.typingUsers.entries()) {
      if (now - typingInfo.timestamp > timeout) {
        this.handleTypingStop(userId, typingInfo.conversationId);
      }
    }
  }
}

// Singleton instance
const websocketService = new WebSocketService();

// Clean up typing indicators every 5 seconds
setInterval(() => {
  websocketService.cleanupTypingIndicators();
}, 5000);

module.exports = websocketService;
