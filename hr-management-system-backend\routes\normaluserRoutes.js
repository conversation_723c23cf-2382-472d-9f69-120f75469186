const express = require('express');
const { authenticate } = require('../middleware/authmiddleware');
const User = require('../models/User');
const LeaveRequest = require('../models/LeaveRequest');
const Attendance = require('../models/Attendance');
const Evaluation = require('../models/Evaluation'); // Import Evaluation model
const Task = require('../models/Task'); // Import Task model
const { createLeaveRequestNotification } = require('../controllers/notificationController');
const { sendPasswordChangeEmail } = require('../utils/emailNotifications');
const bcrypt = require('bcryptjs');
const router = express.Router();

// Get user profile - simplified to just use authentication
router.get('/profile', authenticate, async (req, res) => {
  try {
    console.log('Fetching profile for user ID:', req.user.id);

    // Find the user by ID and exclude the password
    const user = await User.findById(req.user.id).select('-password');

    if (!user) {
      console.error('User not found with ID:', req.user.id);
      return res.status(404).json({ message: 'User not found' });
    }

    console.log('User profile found:', user.email, 'with role:', user.role);
    res.json(user);
  } catch (error) {
    console.error('Error fetching user profile:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Submit a leave request - simplified to just use authentication
router.post('/leave-request', authenticate, async (req, res) => {
  try {
    const { leaveType, startDate, endDate, reason } = req.body;

    // Find the user to get their name
    const user = await User.findById(req.user.id);

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Validate leave type against the enum values in the model
    const validLeaveTypes = ['Annual Leave', 'Sick Leave', 'Personal Leave', 'Maternity/Paternity Leave', 'Bereavement Leave', 'Unpaid Leave'];

    if (!validLeaveTypes.includes(leaveType)) {
      return res.status(400).json({
        message: 'Invalid leave type. Must be one of: ' + validLeaveTypes.join(', ')
      });
    }

    // Create a new leave request
    const newLeaveRequest = new LeaveRequest({
      userId: req.user.id,
      employeeName: user.name,
      leaveType,
      startDate: new Date(startDate),
      endDate: new Date(endDate),
      reason,
      status: 'Pending'
    });

    await newLeaveRequest.save();

    // Create notification for HR about the new leave request
    await createLeaveRequestNotification(newLeaveRequest, 'LEAVE_REQUEST_SUBMITTED');

    res.status(201).json({
      message: 'Leave request submitted successfully',
      leaveRequest: newLeaveRequest
    });
  } catch (error) {
    console.error('Error submitting leave request:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Get user's leave requests - simplified to just use authentication
router.get('/leave-requests', authenticate, async (req, res) => {
  try {
    const userId = req.user.id;
    const { search } = req.query;

    console.log('Fetching leave requests for user ID:', userId);
    console.log('Search query:', search);

    // Build the query
    let query = { userId };

    // Add search functionality
    if (search) {
      query.$or = [
        { leaveType: { $regex: search, $options: 'i' } },
        { reason: { $regex: search, $options: 'i' } },
        { status: { $regex: search, $options: 'i' } }
      ];
    }

    console.log('Final query:', query);

    const leaveRequests = await LeaveRequest.find(query).sort({ createdAt: -1 });

    console.log('Found leave requests:', leaveRequests.length);

    res.json(leaveRequests);
  } catch (error) {
    console.error('Error fetching leave requests:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// ==================== ATTENDANCE ROUTES ====================

// Check-in route
router.post('/attendance/check-in', authenticate, async (req, res) => {
  try {
    const userId = req.user.id;
    const { location } = req.body;

    console.log('Check-in request received for user ID:', userId);
    console.log('Location data:', location);

    // Get today's date in YYYY-MM-DD format
    const today = new Date().toISOString().split('T')[0];
    console.log('Today\'s date:', today);

    // Check if user already has an attendance record for today
    const existingAttendance = await Attendance.findOne({
      userId,
      date: today
    });

    console.log('Existing attendance record:', existingAttendance ? 'Found' : 'None');

    if (existingAttendance) {
      // If already checked in but not checked out, return error
      if (existingAttendance.checkIn && !existingAttendance.checkOut) {
        console.log('User already checked in but not checked out');
        return res.status(400).json({
          message: 'You have already checked in today. Please check out first.'
        });
      }

      // If already checked in and out, return error
      if (existingAttendance.checkIn && existingAttendance.checkOut) {
        console.log('User already completed attendance for today');
        return res.status(400).json({
          message: 'You have already completed your attendance for today.'
        });
      }
    }

    // Create new attendance record
    const checkInTime = new Date();
    console.log('Check-in time:', checkInTime);

    // Determine status based on check-in time
    // For example, if check-in is after 9:00 AM, mark as 'Late'
    const workStartHour = 9; // 9:00 AM
    let status = 'Present';

    if (checkInTime.getHours() > workStartHour ||
        (checkInTime.getHours() === workStartHour && checkInTime.getMinutes() > 0)) {
      status = 'Late';
    }

    console.log('Attendance status:', status);

    const newAttendance = new Attendance({
      userId,
      date: today,
      checkIn: checkInTime,
      status,
      checkInLocation: location || 'Office'
    });

    console.log('Creating new attendance record:', {
      userId: newAttendance.userId,
      date: newAttendance.date,
      checkIn: newAttendance.checkIn,
      status: newAttendance.status
    });

    const savedAttendance = await newAttendance.save();
    console.log('Attendance record saved with ID:', savedAttendance._id);

    res.status(201).json({
      message: 'Check-in successful',
      attendance: {
        id: savedAttendance._id,
        date: savedAttendance.date,
        checkIn: savedAttendance.checkIn,
        status: savedAttendance.status
      }
    });
  } catch (error) {
    console.error('Check-in error:', error);
    res.status(500).json({
      message: 'Server error during check-in',
      error: error.toString(),
      stack: error.stack
    });
  }
});

// Check-out route
router.post('/attendance/check-out', authenticate, async (req, res) => {
  try {
    const userId = req.user.id;
    const { location, notes } = req.body;

    console.log('Check-out request received for user ID:', userId);
    console.log('Request body:', { location, notes });

    // Get today's date in YYYY-MM-DD format
    const today = new Date().toISOString().split('T')[0];
    console.log('Today\'s date:', today);

    // Find today's attendance record
    const attendance = await Attendance.findOne({
      userId,
      date: today
    });

    console.log('Found attendance record:', attendance ? 'Yes' : 'No');

    if (!attendance) {
      console.log('No check-in record found for today');
      return res.status(404).json({ message: 'No check-in record found for today' });
    }

    console.log('Attendance record details:', {
      id: attendance._id,
      date: attendance.date,
      checkIn: attendance.checkIn,
      checkOut: attendance.checkOut,
      status: attendance.status
    });

    if (attendance.checkOut) {
      console.log('User already checked out today');
      return res.status(400).json({ message: 'You have already checked out today' });
    }

    // Update with check-out time
    const checkOutTime = new Date();
    attendance.checkOut = checkOutTime;
    attendance.checkOutLocation = location || 'Office';

    console.log('Setting check-out time:', checkOutTime);

    if (notes) {
      console.log('Adding notes:', notes);
      attendance.notes = notes;
    }

    // Calculate hours worked
    const hoursWorked = attendance.calculateHoursWorked();
    console.log('Hours worked calculated:', hoursWorked);

    const updatedAttendance = await attendance.save();
    console.log('Attendance record updated with check-out time');

    res.status(200).json({
      message: 'Check-out successful',
      attendance: {
        id: updatedAttendance._id,
        date: updatedAttendance.date,
        checkIn: updatedAttendance.checkIn,
        checkOut: updatedAttendance.checkOut,
        hoursWorked: updatedAttendance.hoursWorked,
        status: updatedAttendance.status
      }
    });
  } catch (error) {
    console.error('Check-out error:', error);
    res.status(500).json({
      message: 'Server error during check-out',
      error: error.toString(),
      stack: error.stack
    });
  }
});

// Get current user's attendance status for today
router.get('/attendance/today', authenticate, async (req, res) => {
  try {
    const userId = req.user.id;
    console.log('Fetching attendance for user ID:', userId);

    // Get today's date in YYYY-MM-DD format
    const today = new Date().toISOString().split('T')[0];
    console.log('Today\'s date:', today);

    // Find today's attendance record
    const attendance = await Attendance.findOne({
      userId,
      date: today
    });

    console.log('Found attendance record:', attendance ? 'Yes' : 'No');

    if (!attendance) {
      console.log('No attendance record found for today');
      return res.json({
        status: 'Not checked in',
        checkedIn: false,
        checkedOut: false
      });
    }

    console.log('Attendance details:', {
      id: attendance._id,
      status: attendance.status,
      checkIn: attendance.checkIn,
      checkOut: attendance.checkOut,
      hoursWorked: attendance.hoursWorked
    });

    res.json({
      status: attendance.status,
      checkedIn: !!attendance.checkIn,
      checkedOut: !!attendance.checkOut,
      checkInTime: attendance.checkIn,
      checkOutTime: attendance.checkOut,
      hoursWorked: attendance.hoursWorked
    });
  } catch (error) {
    console.error('Error fetching today\'s attendance:', error);
    res.status(500).json({ message: 'Server error', error: error.toString() });
  }
});

// Get user's attendance history
router.get('/attendance/history', authenticate, async (req, res) => {
  try {
    const userId = req.user.id;
    const { startDate, endDate, search } = req.query;

    console.log('Fetching attendance history for user ID:', userId);
    console.log('Query parameters:', { startDate, endDate, search });

    let query = { userId };

    // Add date range filter if provided
    if (startDate && endDate) {
      query.date = { $gte: startDate, $lte: endDate };
      console.log('Using date range filter:', query.date);
    }

    // Add search functionality
    if (search) {
      query.$or = [
        { status: { $regex: search, $options: 'i' } },
        { notes: { $regex: search, $options: 'i' } }
      ];
    }

    console.log('Final query:', query);

    const attendanceRecords = await Attendance.find(query)
      .sort({ date: -1 }) // Sort by date, newest first
      .limit(search ? 100 : 30); // Increase limit when searching

    console.log('Found attendance records:', attendanceRecords.length);

    if (attendanceRecords.length > 0) {
      console.log('First record:', {
        id: attendanceRecords[0]._id,
        date: attendanceRecords[0].date,
        status: attendanceRecords[0].status
      });
    }

    res.json(attendanceRecords);
  } catch (error) {
    console.error('Error fetching attendance history:', error);
    res.status(500).json({
      message: 'Server error',
      error: error.toString(),
      stack: error.stack
    });
  }
});

// ==================== EVALUATIONS ROUTES ====================

// Get user's evaluations
router.get('/evaluations', authenticate, async (req, res) => {
  try {
    const userId = req.user.id;
    const { search } = req.query;

    console.log('Fetching evaluations for user ID:', userId);

    // Build the query
    const query = {
      userId,
      visibleToUser: true // Only return evaluations that are visible to the user
    };

    // Add search functionality
    if (search) {
      query.$or = [
        { evaluationPeriod: { $regex: search, $options: 'i' } },
        { strengths: { $regex: search, $options: 'i' } },
        { areasForImprovement: { $regex: search, $options: 'i' } },
        { goals: { $regex: search, $options: 'i' } },
        { comments: { $regex: search, $options: 'i' } },
        { status: { $regex: search, $options: 'i' } }
      ];
    }

    // Find all evaluations for this user that are visible to the user
    const evaluations = await Evaluation.find(query)
      .sort({ evaluationDate: -1 }) // Sort by evaluation date, newest first
      .populate('evaluatorId', 'name'); // Populate evaluator details

    console.log('Found evaluations:', evaluations.length);

    res.json(evaluations);
  } catch (error) {
    console.error('Error fetching user evaluations:', error);
    res.status(500).json({
      message: 'Error fetching evaluations',
      error: error.toString()
    });
  }
});

// Get a specific evaluation by ID
router.get('/evaluations/:id', authenticate, async (req, res) => {
  try {
    const userId = req.user.id;
    const evaluationId = req.params.id;

    console.log('Fetching evaluation details:', { userId, evaluationId });

    // Find the evaluation and ensure it belongs to the current user and is visible
    const evaluation = await Evaluation.findOne({
      _id: evaluationId,
      userId: userId,
      visibleToUser: true // Only return evaluations that are visible to the user
    }).populate('evaluatorId', 'name');

    if (!evaluation) {
      console.log('Evaluation not found or does not belong to user');
      return res.status(404).json({ message: 'Evaluation not found' });
    }

    console.log('Found evaluation:', evaluation._id);

    res.json(evaluation);
  } catch (error) {
    console.error('Error fetching evaluation details:', error);
    res.status(500).json({
      message: 'Error fetching evaluation details',
      error: error.toString()
    });
  }
});

// Acknowledge an evaluation (update status to 'Acknowledged')
router.post('/evaluations/:id/acknowledge', authenticate, async (req, res) => {
  try {
    const userId = req.user.id;
    const evaluationId = req.params.id;

    console.log('Acknowledging evaluation:', { userId, evaluationId });

    // Find the evaluation and ensure it belongs to the current user
    const evaluation = await Evaluation.findOne({
      _id: evaluationId,
      userId: userId
    });

    if (!evaluation) {
      console.log('Evaluation not found or does not belong to user');
      return res.status(404).json({ message: 'Evaluation not found' });
    }

    // Update status to 'Acknowledged'
    evaluation.status = 'Acknowledged';
    await evaluation.save();

    console.log('Evaluation acknowledged successfully');

    res.json({
      message: 'Evaluation acknowledged successfully',
      evaluation
    });
  } catch (error) {
    console.error('Error acknowledging evaluation:', error);
    res.status(500).json({
      message: 'Error acknowledging evaluation',
      error: error.toString()
    });
  }
});

// Update user profile
router.put('/update-profile', authenticate, async (req, res) => {
  try {
    const userId = req.user.id;
    const { name, email, job, birthdate } = req.body;

    console.log('Updating profile for user ID:', userId);
    console.log('Update data:', { name, email, job, birthdate });

    // Find the user
    const user = await User.findById(userId);

    if (!user) {
      console.error('User not found with ID:', userId);
      return res.status(404).json({ message: 'User not found' });
    }

    // Update user fields if provided
    if (name) user.name = name;
    if (email) {
      // Check if email is already in use by another user
      const existingUser = await User.findOne({ email, _id: { $ne: userId } });
      if (existingUser) {
        return res.status(400).json({ message: 'Email is already in use by another user' });
      }
      user.email = email;
    }
    if (job) user.job = job;
    if (birthdate) user.birthdate = new Date(birthdate);

    // Save the updated user
    await user.save();

    console.log('User profile updated successfully');

    // Return the updated user without the password
    const updatedUser = await User.findById(userId).select('-password');

    res.json({
      message: 'Profile updated successfully',
      user: updatedUser
    });
  } catch (error) {
    console.error('Error updating profile:', error);
    res.status(500).json({
      message: 'Server error',
      error: error.toString()
    });
  }
});

// Change password
router.put('/change-password', authenticate, async (req, res) => {
  try {
    const userId = req.user.id;
    const userRole = req.user.role;
    const { currentPassword, newPassword } = req.body;

    console.log('Changing password for user:', { userId, role: userRole });

    // Validate request
    if (!currentPassword || !newPassword) {
      console.log('Missing required fields');
      return res.status(400).json({ message: 'Current password and new password are required' });
    }

    // Find the user
    const user = await User.findById(userId);

    if (!user) {
      console.error('User not found with ID:', userId);
      return res.status(404).json({ message: 'User not found' });
    }

    console.log('Found user:', {
      id: user._id,
      email: user.email,
      role: user.role,
      name: user.name,
      job: user.job,
      department: user.department,
      creationDate: user.creationDate ? 'Set' : 'Not set'
    });

    // Verify current password
    console.log('Verifying current password...');
    const isMatch = await user.comparePassword(currentPassword);
    console.log('Password verification result:', isMatch);

    if (!isMatch) {
      console.log('Current password is incorrect');
      return res.status(400).json({ message: 'Current password is incorrect' });
    }

    // Update password
    console.log('Updating password...');
    user.password = newPassword; // The pre-save hook will hash the password
    user.lastPasswordChange = new Date();

    // Make sure all required fields are present
    if (!user.name) user.name = user.name || 'User';
    if (!user.job) user.job = user.job || 'Employee';
    if (!user.creationDate) user.creationDate = user.creationDate || new Date();
    if (!user.department) user.department = user.department || 'Other';

    try {
      await user.save();
      console.log('Password changed successfully');

      // Send email notification about password change
      try {
        await sendPasswordChangeEmail({
          email: user.email,
          to_name: user.name,
          date: new Date().toLocaleString(),
          byAdmin: false
        });
        console.log('Password change email notification sent to user:', user.email);
      } catch (emailError) {
        console.error('Error sending password change email notification:', emailError);
        // Continue with the flow even if email fails
      }

      res.json({ message: 'Password changed successfully' });
    } catch (saveError) {
      console.error('Error saving user after password change:', saveError);
      // Check for validation errors
      if (saveError.name === 'ValidationError') {
        const validationErrors = Object.keys(saveError.errors).map(field => ({
          field,
          message: saveError.errors[field].message
        }));
        console.error('Validation errors:', validationErrors);
        return res.status(400).json({
          message: 'Validation error when saving user',
          errors: validationErrors
        });
      }
      throw saveError; // Re-throw for the outer catch
    }
  } catch (error) {
    console.error('Error changing password:', error);
    res.status(500).json({
      message: 'Server error',
      error: error.toString(),
      stack: error.stack
    });
  }
});

// ==================== TASKS ROUTES ====================

// Get user's assigned tasks
router.get('/tasks', authenticate, async (req, res) => {
  try {
    const userId = req.user.id;
    const {
      status,
      category,
      priority,
      search,
      sortBy = 'deadline',
      sortOrder = 'asc'
    } = req.query;

    console.log('Fetching tasks for user ID:', userId);
    console.log('Query parameters:', { status, category, priority, search, sortBy, sortOrder });

    // Build the query
    let query = { assignedTo: userId };

    // Add filters if provided
    if (status) query.status = status;
    if (category) query.category = category;
    if (priority) query.priority = priority;

    // Add search functionality
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { status: { $regex: search, $options: 'i' } },
        { priority: { $regex: search, $options: 'i' } },
        { category: { $regex: search, $options: 'i' } }
      ];
    }

    console.log('Final query:', query);

    // Build the sort object
    const sort = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

    console.log('Sort criteria:', sort);

    // Execute the query
    const tasks = await Task.find(query)
      .sort(sort)
      .populate('assignedTo', 'name email');

    console.log('Found tasks:', tasks.length);

    res.json(tasks);
  } catch (error) {
    console.error('Error fetching tasks:', error);
    res.status(500).json({
      message: 'Error fetching tasks',
      error: error.toString()
    });
  }
});

// Get a specific task by ID
router.get('/tasks/:id', authenticate, async (req, res) => {
  try {
    const userId = req.user.id;
    const taskId = req.params.id;

    console.log('Fetching task details:', { userId, taskId });

    // Find the task and ensure it's assigned to the current user
    const task = await Task.findOne({
      _id: taskId,
      assignedTo: userId
    }).populate('assignedTo', 'name email');

    if (!task) {
      console.log('Task not found or not assigned to user');
      return res.status(404).json({ message: 'Task not found' });
    }

    console.log('Found task:', task._id);

    res.json(task);
  } catch (error) {
    console.error('Error fetching task details:', error);
    res.status(500).json({
      message: 'Error fetching task details',
      error: error.toString()
    });
  }
});

// Update task status and progress
router.put('/tasks/:id/update-status', authenticate, async (req, res) => {
  try {
    const userId = req.user.id;
    const taskId = req.params.id;
    const { status, progress, notes } = req.body;

    console.log('Updating task status:', { userId, taskId, status, progress });

    // Find the task and ensure it's assigned to the current user
    const task = await Task.findOne({
      _id: taskId,
      assignedTo: userId
    });

    if (!task) {
      console.log('Task not found or not assigned to user');
      return res.status(404).json({ message: 'Task not found' });
    }

    // Update task fields
    if (status) task.status = status;
    if (progress !== undefined) task.progress = progress;
    if (notes) {
      // Add a new note with timestamp
      const newNote = {
        text: notes,
        timestamp: new Date(),
        author: userId
      };

      if (!task.notes) {
        task.notes = [];
      }

      task.notes.push(newNote);
    }

    // Save the updated task
    await task.save();

    console.log('Task updated successfully');

    res.json({
      message: 'Task updated successfully',
      task
    });
  } catch (error) {
    console.error('Error updating task:', error);
    res.status(500).json({
      message: 'Error updating task',
      error: error.toString()
    });
  }
});

module.exports = router;
