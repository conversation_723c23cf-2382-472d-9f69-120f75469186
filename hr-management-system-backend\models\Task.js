const mongoose = require('mongoose');

const taskSchema = new mongoose.Schema({
  // Task details
  title: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    required: false,
    trim: true,
    default: ''
  },
  category: {
    type: String,
    enum: ['General', 'Development', 'Design', 'Marketing', 'HR', 'Finance', 'Operations', 'Project', 'Administrative', 'Training', 'Evaluation', 'Other'],
    default: 'General'
  },
  priority: {
    type: String,
    enum: ['Low', 'Medium', 'High', 'Urgent'],
    default: 'Medium'
  },

  // Dates
  createdAt: {
    type: Date,
    default: Date.now
  },
  deadline: {
    type: Date,
    required: true
  },

  // Task creator (HR)
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },

  // Task assignee (employee) - now optional
  assignedTo: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: false,
    default: null
  },

  // Task status
  status: {
    type: String,
    enum: ['Unassigned', 'Assigned', 'In Progress', 'Completed', 'Needs Revision'],
    default: 'Unassigned'
  },

  // Task progress (0-100%)
  progress: {
    type: Number,
    min: 0,
    max: 100,
    default: 0
  },

  // Feedback from HR
  feedback: {
    type: String,
    default: ''
  },

  // Completion details
  completedAt: {
    type: Date,
    default: null
  },

  // Visibility to managers
  visibleToManagers: {
    type: Boolean,
    default: false
  },

  // Attachments (file paths)
  attachments: [{
    fileName: String,
    filePath: String,
    uploadedAt: {
      type: Date,
      default: Date.now
    }
  }],

  // Task updates/comments
  updates: [{
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    message: String,
    timestamp: {
      type: Date,
      default: Date.now
    }
  }],

  // Notification settings
  notifications: {
    reminderSent: {
      type: Boolean,
      default: false
    },
    lastReminderDate: {
      type: Date,
      default: null
    }
  },

  // GEK (General Estimating Knowledge) fields
  gekFitScore: {
    type: Number,
    min: 0,
    max: 100,
    default: 0
  },
  estimatedCompletionTime: {
    type: Number, // in hours
    min: 0,
    default: 8
  },
  actualCompletionTime: {
    type: Number, // in hours, calculated when task is completed
    min: 0,
    default: null
  }
}, { timestamps: true });

// Create indexes for better query performance
taskSchema.index({ createdBy: 1 });
taskSchema.index({ assignedTo: 1 });
taskSchema.index({ status: 1 });
taskSchema.index({ deadline: 1 });
taskSchema.index({ category: 1 });

// Virtual for checking if task is overdue
taskSchema.virtual('isOverdue').get(function() {
  return this.deadline < new Date() && this.status !== 'Completed';
});

// Virtual for days remaining until deadline
taskSchema.virtual('daysRemaining').get(function() {
  const today = new Date();
  const deadline = new Date(this.deadline);
  const diffTime = deadline - today;
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays > 0 ? diffDays : 0;
});

// Method to mark task as completed
taskSchema.methods.markAsCompleted = function(userId) {
  this.status = 'Completed';
  this.progress = 100;
  this.completedAt = new Date();

  // Calculate actual completion time in hours
  if (this.createdAt) {
    const timeDiff = this.completedAt - this.createdAt;
    this.actualCompletionTime = Math.round(timeDiff / (1000 * 60 * 60)); // Convert to hours
  }

  this.updates.push({
    userId,
    message: 'Task marked as completed',
    timestamp: new Date()
  });
  return this.save();
};

// Method to add an update to the task
taskSchema.methods.addUpdate = function(userId, message) {
  this.updates.push({
    userId,
    message,
    timestamp: new Date()
  });
  return this.save();
};

// Check if the model already exists to prevent recompilation
const Task = mongoose.models.Task || mongoose.model('Task', taskSchema);

module.exports = Task;
