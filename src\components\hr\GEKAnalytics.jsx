import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Alert,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Chip,
  Button
} from '@mui/material';
import {
  TrendingUp,
  Assessment,
  Group,
  Speed,
  Timeline,
  Star,
  Business,
  Refresh
} from '@mui/icons-material';
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell
} from 'recharts';
import { showErrorToast, TOAST_CATEGORIES } from '../../Utils/toastUtils';
import GEKService from '../../Services/GEKService';

/**
 * GEK Analytics Component
 * Displays comprehensive analytics and insights for the GEK system
 */
const GEKAnalytics = () => {
  // State management
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [timePeriod, setTimePeriod] = useState(90);
  const [trendInterval, setTrendInterval] = useState('week');
  
  // Analytics data
  const [overviewData, setOverviewData] = useState(null);
  const [departmentData, setDepartmentData] = useState([]);
  const [trendsData, setTrendsData] = useState([]);

  // Chart colors
  const COLORS = ['#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#8dd1e1', '#d084d0'];

  // Load analytics data on component mount and when filters change
  useEffect(() => {
    loadAnalyticsData();
  }, [timePeriod, trendInterval]);

  // Load all analytics data
  const loadAnalyticsData = async () => {
    setLoading(true);
    setError(null);

    try {
      // Load all analytics in parallel
      const [overviewResponse, departmentResponse, trendsResponse] = await Promise.all([
        GEKService.getAnalyticsOverview(timePeriod),
        GEKService.getDepartmentPerformance(timePeriod),
        GEKService.getPerformanceTrends(timePeriod, trendInterval)
      ]);

      setOverviewData(overviewResponse.analytics);
      setDepartmentData(departmentResponse.analytics.departmentPerformance);
      setTrendsData(trendsResponse.analytics.trends);
    } catch (err) {
      console.error('Error loading analytics data:', err);
      setError('Failed to load analytics data');
      showErrorToast('Failed to load analytics data', TOAST_CATEGORIES.GENERAL, 'analyticsLoadFailed');
    } finally {
      setLoading(false);
    }
  };

  // Handle time period change
  const handleTimePeriodChange = (event) => {
    setTimePeriod(event.target.value);
  };

  // Handle trend interval change
  const handleTrendIntervalChange = (event) => {
    setTrendInterval(event.target.value);
  };

  // Format trend data for charts
  const formatTrendsForChart = () => {
    return trendsData.map((trend, index) => ({
      name: trendInterval === 'week' 
        ? `Week ${trend.period.week}/${trend.period.year}`
        : trendInterval === 'month'
        ? `${trend.period.month}/${trend.period.year}`
        : `${trend.period.day}/${trend.period.month}`,
      fitScore: trend.avgFitScore,
      completionTime: trend.avgCompletionTime,
      confidence: trend.avgConfidenceScore,
      estimates: trend.totalEstimates
    }));
  };

  // Format category data for pie chart
  const formatCategoryData = () => {
    if (!overviewData?.avgFitScoresByCategory) return [];
    
    return overviewData.avgFitScoresByCategory.map((category) => ({
      name: category._id,
      value: category.avgFitScore,
      count: category.count
    }));
  };

  return (
    <Box>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom fontWeight={700}>
          GEK Analytics & Insights
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Comprehensive performance analytics and insights for the General Estimating Knowledge system.
        </Typography>
      </Box>

      {/* Error Alert */}
      {error && (
        <Alert
          severity="error"
          sx={{ mb: 3 }}
          action={
            <Button color="inherit" size="small" onClick={loadAnalyticsData}>
              Retry
            </Button>
          }
        >
          {error}
        </Alert>
      )}

      {/* Controls */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>Time Period</InputLabel>
                <Select
                  value={timePeriod}
                  label="Time Period"
                  onChange={handleTimePeriodChange}
                >
                  <MenuItem value={30}>Last 30 Days</MenuItem>
                  <MenuItem value={60}>Last 60 Days</MenuItem>
                  <MenuItem value={90}>Last 90 Days</MenuItem>
                  <MenuItem value={180}>Last 6 Months</MenuItem>
                  <MenuItem value={365}>Last Year</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>Trend Interval</InputLabel>
                <Select
                  value={trendInterval}
                  label="Trend Interval"
                  onChange={handleTrendIntervalChange}
                >
                  <MenuItem value="day">Daily</MenuItem>
                  <MenuItem value="week">Weekly</MenuItem>
                  <MenuItem value="month">Monthly</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={3}>
              <Button
                variant="outlined"
                onClick={loadAnalyticsData}
                startIcon={<Refresh />}
                disabled={loading}
                fullWidth
              >
                Refresh Data
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 8 }}>
          <CircularProgress size={60} />
        </Box>
      ) : (
        <>
          {/* Overview Cards */}
          {overviewData && (
            <Grid container spacing={3} sx={{ mb: 4 }}>
              <Grid item xs={12} sm={6} md={3}>
                <Card>
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Assessment sx={{ mr: 1, color: 'primary.main' }} />
                      <Typography variant="h6">Total Estimates</Typography>
                    </Box>
                    <Typography variant="h4" fontWeight="bold" color="primary">
                      {overviewData.totalEstimates}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Last {timePeriod} days
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={12} sm={6} md={3}>
                <Card>
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Speed sx={{ mr: 1, color: 'success.main' }} />
                      <Typography variant="h6">Avg Fit Score</Typography>
                    </Box>
                    <Typography variant="h4" fontWeight="bold" color="success.main">
                      {overviewData.avgFitScoresByCategory.length > 0 
                        ? Math.round(overviewData.avgFitScoresByCategory.reduce((sum, cat) => sum + cat.avgFitScore, 0) / overviewData.avgFitScoresByCategory.length)
                        : 0}%
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Across all categories
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={12} sm={6} md={3}>
                <Card>
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Star sx={{ mr: 1, color: 'warning.main' }} />
                      <Typography variant="h6">Top Performers</Typography>
                    </Box>
                    <Typography variant="h4" fontWeight="bold" color="warning.main">
                      {overviewData.topPerformers.length}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      High-performing employees
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={12} sm={6} md={3}>
                <Card>
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Business sx={{ mr: 1, color: 'info.main' }} />
                      <Typography variant="h6">Categories</Typography>
                    </Box>
                    <Typography variant="h4" fontWeight="bold" color="info.main">
                      {overviewData.avgFitScoresByCategory.length}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Active task categories
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          )}

          {/* Charts Row */}
          <Grid container spacing={3} sx={{ mb: 4 }}>
            {/* Performance Trends Chart */}
            <Grid item xs={12} lg={8}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    <Timeline sx={{ mr: 1, verticalAlign: 'middle' }} />
                    Performance Trends
                  </Typography>
                  <Box sx={{ height: 300 }}>
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart data={formatTrendsForChart()}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="name" />
                        <YAxis />
                        <Tooltip />
                        <Legend />
                        <Line 
                          type="monotone" 
                          dataKey="fitScore" 
                          stroke="#8884d8" 
                          name="Avg Fit Score (%)"
                          strokeWidth={2}
                        />
                        <Line 
                          type="monotone" 
                          dataKey="confidence" 
                          stroke="#82ca9d" 
                          name="Avg Confidence (%)"
                          strokeWidth={2}
                        />
                      </LineChart>
                    </ResponsiveContainer>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            {/* Category Performance Pie Chart */}
            <Grid item xs={12} lg={4}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Category Performance
                  </Typography>
                  <Box sx={{ height: 300 }}>
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={formatCategoryData()}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          label={({ name, value }) => `${name}: ${Math.round(value)}%`}
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="value"
                        >
                          {formatCategoryData().map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                          ))}
                        </Pie>
                        <Tooltip />
                      </PieChart>
                    </ResponsiveContainer>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Department Performance and Top Performers */}
          <Grid container spacing={3}>
            {/* Department Performance Chart */}
            <Grid item xs={12} lg={8}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    <Business sx={{ mr: 1, verticalAlign: 'middle' }} />
                    Department Performance
                  </Typography>
                  <Box sx={{ height: 400 }}>
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart data={departmentData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="department" />
                        <YAxis />
                        <Tooltip />
                        <Legend />
                        <Bar
                          dataKey="avgFitScore"
                          fill="#8884d8"
                          name="Avg Fit Score (%)"
                        />
                        <Bar
                          dataKey="avgConfidenceScore"
                          fill="#82ca9d"
                          name="Avg Confidence (%)"
                        />
                      </BarChart>
                    </ResponsiveContainer>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            {/* Top Performers List */}
            <Grid item xs={12} lg={4}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    <Star sx={{ mr: 1, verticalAlign: 'middle' }} />
                    Top Performers
                  </Typography>
                  {overviewData?.topPerformers?.length > 0 ? (
                    <List>
                      {overviewData.topPerformers.slice(0, 8).map((performer, index) => (
                        <ListItem key={performer.user._id} sx={{ px: 0 }}>
                          <ListItemAvatar>
                            <Avatar sx={{
                              bgcolor: index < 3 ? 'gold' : 'primary.main',
                              width: 32,
                              height: 32
                            }}>
                              {index + 1}
                            </Avatar>
                          </ListItemAvatar>
                          <ListItemText
                            primary={
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                <Typography variant="body2" fontWeight="medium">
                                  {performer.user.name}
                                </Typography>
                                {index < 3 && (
                                  <Chip
                                    label="Top 3"
                                    size="small"
                                    color="secondary"
                                  />
                                )}
                              </Box>
                            }
                            secondary={
                              <Box>
                                <Typography variant="caption" color="text.secondary">
                                  {performer.user.job} • {performer.user.department}
                                </Typography>
                                <Box sx={{ display: 'flex', gap: 2, mt: 0.5 }}>
                                  <Typography variant="caption">
                                    Fit Score: <strong>{performer.avgFitScore}%</strong>
                                  </Typography>
                                  <Typography variant="caption">
                                    Estimates: <strong>{performer.totalEstimates}</strong>
                                  </Typography>
                                </Box>
                              </Box>
                            }
                          />
                        </ListItem>
                      ))}
                    </List>
                  ) : (
                    <Typography color="text.secondary" align="center" sx={{ py: 4 }}>
                      No top performers data available
                    </Typography>
                  )}
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Department Details Table */}
          {departmentData.length > 0 && (
            <Card sx={{ mt: 3 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Department Performance Details
                </Typography>
                <Box sx={{ overflowX: 'auto' }}>
                  <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                    <thead>
                      <tr style={{ borderBottom: '2px solid #e0e0e0' }}>
                        <th style={{ padding: '12px', textAlign: 'left' }}>Department</th>
                        <th style={{ padding: '12px', textAlign: 'center' }}>Employees</th>
                        <th style={{ padding: '12px', textAlign: 'center' }}>Avg Fit Score</th>
                        <th style={{ padding: '12px', textAlign: 'center' }}>Avg Completion Time</th>
                        <th style={{ padding: '12px', textAlign: 'center' }}>Confidence Score</th>
                        <th style={{ padding: '12px', textAlign: 'center' }}>Total Estimates</th>
                      </tr>
                    </thead>
                    <tbody>
                      {departmentData
                        .sort((a, b) => b.avgFitScore - a.avgFitScore)
                        .map((dept, index) => (
                        <tr
                          key={dept.department}
                          style={{
                            borderBottom: '1px solid #f0f0f0',
                            backgroundColor: index % 2 === 0 ? '#fafafa' : 'white'
                          }}
                        >
                          <td style={{ padding: '12px', fontWeight: 'medium' }}>
                            {dept.department || 'Unknown'}
                          </td>
                          <td style={{ padding: '12px', textAlign: 'center' }}>
                            {dept.employeeCount}
                          </td>
                          <td style={{ padding: '12px', textAlign: 'center' }}>
                            <Chip
                              label={`${dept.avgFitScore}%`}
                              size="small"
                              color={dept.avgFitScore >= 80 ? 'success' : dept.avgFitScore >= 60 ? 'warning' : 'error'}
                            />
                          </td>
                          <td style={{ padding: '12px', textAlign: 'center' }}>
                            {dept.avgCompletionTime}h
                          </td>
                          <td style={{ padding: '12px', textAlign: 'center' }}>
                            {dept.avgConfidenceScore}%
                          </td>
                          <td style={{ padding: '12px', textAlign: 'center' }}>
                            {dept.totalEstimates}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </Box>
              </CardContent>
            </Card>
          )}

          {/* Insights and Recommendations */}
          <Card sx={{ mt: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                <TrendingUp sx={{ mr: 1, verticalAlign: 'middle' }} />
                Key Insights & Recommendations
              </Typography>
              <Grid container spacing={2}>
                {overviewData && (
                  <>
                    {/* Best Performing Category */}
                    {overviewData.avgFitScoresByCategory.length > 0 && (
                      <Grid item xs={12} md={6}>
                        <Box sx={{ p: 2, bgcolor: 'success.light', borderRadius: 1, color: 'success.contrastText' }}>
                          <Typography variant="subtitle2" gutterBottom>
                            🏆 Best Performing Category
                          </Typography>
                          <Typography variant="body2">
                            <strong>{overviewData.avgFitScoresByCategory[0]._id}</strong> has the highest average fit score of{' '}
                            <strong>{Math.round(overviewData.avgFitScoresByCategory[0].avgFitScore)}%</strong> with{' '}
                            {overviewData.avgFitScoresByCategory[0].count} estimates.
                          </Typography>
                        </Box>
                      </Grid>
                    )}

                    {/* Top Department */}
                    {departmentData.length > 0 && (
                      <Grid item xs={12} md={6}>
                        <Box sx={{ p: 2, bgcolor: 'info.light', borderRadius: 1, color: 'info.contrastText' }}>
                          <Typography variant="subtitle2" gutterBottom>
                            🏢 Top Performing Department
                          </Typography>
                          <Typography variant="body2">
                            <strong>{departmentData[0].department}</strong> leads with{' '}
                            <strong>{departmentData[0].avgFitScore}%</strong> average fit score and{' '}
                            {departmentData[0].employeeCount} active employees.
                          </Typography>
                        </Box>
                      </Grid>
                    )}

                    {/* System Health */}
                    <Grid item xs={12} md={6}>
                      <Box sx={{ p: 2, bgcolor: 'warning.light', borderRadius: 1, color: 'warning.contrastText' }}>
                        <Typography variant="subtitle2" gutterBottom>
                          📊 System Health
                        </Typography>
                        <Typography variant="body2">
                          Generated <strong>{overviewData.totalEstimates}</strong> estimates in the last {timePeriod} days.{' '}
                          {overviewData.totalEstimates > 100 ? 'System is highly active!' : 'Consider increasing system usage.'}
                        </Typography>
                      </Box>
                    </Grid>

                    {/* Improvement Opportunity */}
                    {overviewData.avgFitScoresByCategory.length > 1 && (
                      <Grid item xs={12} md={6}>
                        <Box sx={{ p: 2, bgcolor: 'error.light', borderRadius: 1, color: 'error.contrastText' }}>
                          <Typography variant="subtitle2" gutterBottom>
                            🎯 Improvement Opportunity
                          </Typography>
                          <Typography variant="body2">
                            <strong>{overviewData.avgFitScoresByCategory[overviewData.avgFitScoresByCategory.length - 1]._id}</strong>{' '}
                            category has the lowest fit score. Consider additional training or task redistribution.
                          </Typography>
                        </Box>
                      </Grid>
                    )}
                  </>
                )}
              </Grid>
            </CardContent>
          </Card>
        </>
      )}
    </Box>
  );
};

export default GEKAnalytics;
