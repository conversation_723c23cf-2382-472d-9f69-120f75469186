const { spawn } = require('child_process');
const path = require('path');
const User = require('../models/User');
const Task = require('../models/Task');
const Attendance = require('../models/Attendance');
const Evaluation = require('../models/Evaluation');

// Helper functions for generating insights
// ==================== DETAILED TASK PERFORMANCE INSIGHTS ====================
function generateDetailedTaskPerformanceInsight(
  tasks,
  completedTasks,
  onTimeTasks,
  lateCompletedTasks,
  highPriorityCompleted,
  highPriorityTotal,
  taskPerformanceRating
) {
  if (tasks.length === 0) {
    return "No task data available to evaluate performance.";
  }

  const completionRate = completedTasks / tasks.length;
  const timelinessRate = completedTasks > 0 ? onTimeTasks / completedTasks : 0;
  const lateRate = completedTasks > 0 ? lateCompletedTasks / completedTasks : 0;
  const highPriorityRate = highPriorityTotal > 0 ? highPriorityCompleted / highPriorityTotal : 0;

  // Build a detailed, data-driven insight
  let insight = "";

  // Task completion analysis
  if (completionRate >= 0.9) {
    insight += `Excellent task completion rate of ${Math.round(completionRate * 100)}%. `;
  } else if (completionRate >= 0.75) {
    insight += `Good task completion rate of ${Math.round(completionRate * 100)}%. `;
  } else if (completionRate >= 0.6) {
    insight += `Satisfactory task completion rate of ${Math.round(completionRate * 100)}%. `;
  } else {
    insight += `Below average task completion rate of ${Math.round(completionRate * 100)}%. `;
  }

  // Timeliness analysis
  if (completedTasks > 0) {
    if (timelinessRate >= 0.9) {
      insight += `Excellent timeliness with ${Math.round(timelinessRate * 100)}% of tasks completed on or before deadline. `;
    } else if (timelinessRate >= 0.75) {
      insight += `Good timeliness with ${Math.round(timelinessRate * 100)}% of tasks completed on time. `;
    } else if (timelinessRate >= 0.6) {
      insight += `Satisfactory timeliness with ${Math.round(timelinessRate * 100)}% of tasks completed on time. `;
    } else {
      insight += `Needs improvement in timeliness with only ${Math.round(timelinessRate * 100)}% of tasks completed on time. `;
    }

    // Add details about late tasks if applicable
    if (lateRate > 0.1) {
      insight += `${Math.round(lateRate * 100)}% of completed tasks were submitted after the deadline. `;
    }
  }

  // High priority task analysis
  if (highPriorityTotal > 0) {
    if (highPriorityRate >= 0.9) {
      insight += `Excellent handling of high-priority tasks with ${Math.round(highPriorityRate * 100)}% completion rate. `;
    } else if (highPriorityRate >= 0.75) {
      insight += `Good handling of high-priority tasks with ${Math.round(highPriorityRate * 100)}% completion rate. `;
    } else if (highPriorityRate >= 0.6) {
      insight += `Satisfactory handling of high-priority tasks with ${Math.round(highPriorityRate * 100)}% completion rate. `;
    } else {
      insight += `Needs improvement in handling high-priority tasks with only ${Math.round(highPriorityRate * 100)}% completion rate. `;
    }
  }

  // Overall performance summary
  if (taskPerformanceRating >= 4.5) {
    insight += `Overall, demonstrates exceptional task performance (${taskPerformanceRating.toFixed(1)}/5.0) with consistent completion of assignments on time and with high quality.`;
  } else if (taskPerformanceRating >= 4.0) {
    insight += `Overall, demonstrates very good task performance (${taskPerformanceRating.toFixed(1)}/5.0) with reliable completion of assignments.`;
  } else if (taskPerformanceRating >= 3.5) {
    insight += `Overall, demonstrates good task performance (${taskPerformanceRating.toFixed(1)}/5.0) with consistent work quality.`;
  } else if (taskPerformanceRating >= 3.0) {
    insight += `Overall, demonstrates satisfactory task performance (${taskPerformanceRating.toFixed(1)}/5.0) but has room for improvement.`;
  } else if (taskPerformanceRating >= 2.5) {
    insight += `Overall, demonstrates below average task performance (${taskPerformanceRating.toFixed(1)}/5.0) and should focus on improving completion rates and timeliness.`;
  } else {
    insight += `Overall, demonstrates poor task performance (${taskPerformanceRating.toFixed(1)}/5.0) and requires significant improvement in task management and completion.`;
  }

  return insight;
}

// ==================== DETAILED ATTENDANCE INSIGHTS ====================
function generateDetailedAttendanceInsight(
  records,
  presentCount,
  lateCount,
  absentCount,
  excusedAbsenceCount,
  maxConsecutiveAbsences,
  patternViolations,
  attendanceRating
) {
  if (records.length === 0) {
    return "No attendance data available for analysis.";
  }

  const presentRate = presentCount / records.length;
  const lateRate = lateCount / records.length;
  const absentRate = absentCount / records.length;
  const excusedRate = absentCount > 0 ? excusedAbsenceCount / absentCount : 1.0;

  // Build a detailed, data-driven insight
  let insights = [];

  // Presence analysis
  if (presentRate >= 0.95) {
    insights.push(`Excellent attendance with ${Math.round(presentRate * 100)}% presence rate`);
  } else if (presentRate >= 0.9) {
    insights.push(`Very good attendance with ${Math.round(presentRate * 100)}% presence rate`);
  } else if (presentRate >= 0.85) {
    insights.push(`Good attendance with ${Math.round(presentRate * 100)}% presence rate`);
  } else if (presentRate >= 0.8) {
    insights.push(`Satisfactory attendance with ${Math.round(presentRate * 100)}% presence rate`);
  } else {
    insights.push(`Below average attendance with ${Math.round(presentRate * 100)}% presence rate`);
  }

  // Punctuality analysis
  if (lateRate > 0) {
    if (lateRate <= 0.05) {
      insights.push(`Excellent punctuality with only ${Math.round(lateRate * 100)}% late arrivals`);
    } else if (lateRate <= 0.1) {
      insights.push(`Good punctuality with ${Math.round(lateRate * 100)}% late arrivals`);
    } else if (lateRate <= 0.15) {
      insights.push(`Satisfactory punctuality with ${Math.round(lateRate * 100)}% late arrivals`);
    } else {
      insights.push(`Needs improvement in punctuality with ${Math.round(lateRate * 100)}% late arrivals`);
    }
  } else {
    insights.push("Perfect punctuality with no late arrivals");
  }

  // Absence analysis
  if (absentCount > 0) {
    if (excusedRate >= 0.9) {
      insights.push(`${Math.round(excusedRate * 100)}% of absences were properly excused`);
    } else if (excusedRate >= 0.7) {
      insights.push(`${Math.round(excusedRate * 100)}% of absences were excused, but some were not properly documented`);
    } else {
      insights.push(`Only ${Math.round(excusedRate * 100)}% of absences were properly excused, which is concerning`);
    }

    if (maxConsecutiveAbsences > 2) {
      insights.push(`Had ${maxConsecutiveAbsences} consecutive days of absence at one point`);
    }
  }

  // Pattern analysis
  if (patternViolations > 1.5) {
    insights.push("Shows concerning patterns in attendance that may need attention");
  } else if (patternViolations > 0.5) {
    insights.push("Shows some minor patterns in attendance that should be monitored");
  }

  // Overall attendance summary
  if (attendanceRating >= 4.5) {
    insights.push(`Overall attendance rating is exceptional (${attendanceRating.toFixed(1)}/5.0)`);
  } else if (attendanceRating >= 4.0) {
    insights.push(`Overall attendance rating is very good (${attendanceRating.toFixed(1)}/5.0)`);
  } else if (attendanceRating >= 3.5) {
    insights.push(`Overall attendance rating is good (${attendanceRating.toFixed(1)}/5.0)`);
  } else if (attendanceRating >= 3.0) {
    insights.push(`Overall attendance rating is satisfactory (${attendanceRating.toFixed(1)}/5.0)`);
  } else if (attendanceRating >= 2.5) {
    insights.push(`Overall attendance rating is below average (${attendanceRating.toFixed(1)}/5.0)`);
  } else {
    insights.push(`Overall attendance rating is poor (${attendanceRating.toFixed(1)}/5.0)`);
  }

  return insights.join(". ") + ".";
}

// ==================== STRENGTHS BASED ON TASKS AND ATTENDANCE ====================
function generateTaskAttendanceStrengths(taskPerformanceRating, attendanceRating) {
  const strengths = [];

  // Task performance strengths
  if (taskPerformanceRating >= 4.5) {
    strengths.push("Task Performance: Demonstrates exceptional task management with outstanding completion rates and timeliness. Consistently delivers high-quality work and effectively prioritizes tasks based on importance and deadlines.");
  } else if (taskPerformanceRating >= 4.0) {
    strengths.push("Task Performance: Shows excellent task management skills with high completion rates. Consistently meets deadlines and handles high-priority tasks effectively.");
  } else if (taskPerformanceRating >= 3.5) {
    strengths.push("Task Performance: Demonstrates good task management with reliable completion rates. Generally meets deadlines and produces quality work.");
  }

  // Attendance strengths
  if (attendanceRating >= 4.5) {
    strengths.push("Attendance: Demonstrates exceptional attendance and punctuality. Shows outstanding reliability and commitment through consistent presence and timeliness.");
  } else if (attendanceRating >= 4.0) {
    strengths.push("Attendance: Shows excellent attendance habits with very good presence rates and punctuality. Absences are properly documented and excused.");
  } else if (attendanceRating >= 3.5) {
    strengths.push("Attendance: Demonstrates good attendance with reliable presence and punctuality. Generally follows proper procedures for absences.");
  }

  if (strengths.length === 0) {
    return "The AI evaluation system could not identify specific strengths in task performance or attendance based on available data. Consider discussing with the employee to identify their strengths in these areas.";
  }

  return strengths.join("\n\n");
}

// ==================== AREAS FOR IMPROVEMENT BASED ON TASKS AND ATTENDANCE ====================
function generateTaskAttendanceImprovements(taskPerformanceRating, attendanceRating) {
  const improvements = [];

  // Task performance improvements
  if (taskPerformanceRating < 3.0) {
    improvements.push("Task Performance: Significant improvement needed in task management. Focus on increasing task completion rates and meeting deadlines consistently. Consider implementing a personal task tracking system and breaking down larger tasks into manageable steps with interim deadlines.");
  } else if (taskPerformanceRating < 3.5) {
    improvements.push("Task Performance: Improvement needed in task management. Work on increasing completion rates and timeliness. Consider prioritizing tasks more effectively and allocating appropriate time for each assignment.");
  } else if (taskPerformanceRating < 4.0) {
    improvements.push("Task Performance: Consider enhancing task management by improving timeliness and quality of deliverables. Focus particularly on high-priority tasks and meeting deadlines consistently.");
  }

  // Attendance improvements
  if (attendanceRating < 3.0) {
    improvements.push("Attendance: Significant improvement needed in attendance and punctuality. Focus on consistent presence and arriving on time. Ensure all absences are properly communicated and documented according to company policy.");
  } else if (attendanceRating < 3.5) {
    improvements.push("Attendance: Improvement needed in attendance patterns. Work on reducing late arrivals and ensuring all absences are properly excused and documented.");
  } else if (attendanceRating < 4.0) {
    improvements.push("Attendance: Consider improving punctuality and reducing any patterns of absence. Ensure all time off is properly requested and documented in advance when possible.");
  }

  if (improvements.length === 0) {
    return "The AI evaluation system did not identify specific areas requiring significant improvement in task performance or attendance based on available data. Consider discussing with the employee to identify potential growth areas.";
  }

  return improvements.join("\n\n");
}

// ==================== GOALS BASED ON TASKS AND ATTENDANCE ====================
function generateTaskAttendanceGoals(taskPerformanceRating, attendanceRating) {
  const goals = [];

  // Task performance goals
  if (taskPerformanceRating < 3.0) {
    goals.push("Task Performance Goal: Increase task completion rate by at least 25% in the next evaluation period. Implement a daily task prioritization system and track progress weekly. Meet with supervisor bi-weekly to review progress and address any obstacles.");
  } else if (taskPerformanceRating < 3.5) {
    goals.push("Task Performance Goal: Improve task completion rate by 15% and ensure at least 80% of tasks are completed on time. Focus particularly on high-priority assignments and meeting their deadlines consistently.");
  } else if (taskPerformanceRating < 4.0) {
    goals.push("Task Performance Goal: Enhance task management by achieving a 90% on-time completion rate for all assigned tasks. Develop strategies for better handling of complex or high-priority assignments.");
  } else {
    goals.push("Task Performance Goal: Maintain excellent task performance while taking on more complex responsibilities. Consider mentoring colleagues on effective task management strategies.");
  }

  // Attendance goals
  if (attendanceRating < 3.0) {
    goals.push("Attendance Goal: Achieve at least 90% presence rate and reduce late arrivals by 75% in the next evaluation period. Develop a reliable morning routine to ensure consistent on-time arrival.");
  } else if (attendanceRating < 3.5) {
    goals.push("Attendance Goal: Improve overall attendance to achieve at least 95% presence rate and ensure all absences are properly documented and excused according to company policy.");
  } else if (attendanceRating < 4.0) {
    goals.push("Attendance Goal: Enhance punctuality by reducing late arrivals to less than 5% and maintain consistent attendance without patterns of absence.");
  } else {
    goals.push("Attendance Goal: Maintain excellent attendance record while helping to establish best practices for the team in attendance and punctuality.");
  }

  return goals.join("\n\n");
}

/**
 * Generate an AI evaluation for a user
 * @param {Object} userData - User data
 * @param {Array} tasks - User's tasks
 * @param {Array} attendanceRecords - User's attendance records
 * @param {Array} previousEvaluations - User's previous evaluations
 * @returns {Promise<Object>} - AI generated evaluation
 */
const generateAIEvaluation = (userData, tasks, attendanceRecords, previousEvaluations) => {
  return new Promise((resolve, reject) => {
    try {
      console.log('Generating AI evaluation for user:', userData.name);

      // Prepare data for Python script
      const userDataStr = JSON.stringify(userData);
      const tasksStr = JSON.stringify(tasks);
      const attendanceStr = JSON.stringify(attendanceRecords);
      const evaluationsStr = JSON.stringify(previousEvaluations);

      // Path to Python script
      const scriptPath = path.join(__dirname, '..', 'NLP', 'aiEvaluation.py');

      console.log('Executing Python script:', scriptPath);

      // Spawn Python process
      const pythonProcess = spawn('python', [
        scriptPath,
        userDataStr,
        tasksStr,
        attendanceStr,
        evaluationsStr
      ]);

      let result = '';
      let errorOutput = '';

      // Collect data from script
      pythonProcess.stdout.on('data', (data) => {
        result += data.toString();
      });

      // Collect error output
      pythonProcess.stderr.on('data', (data) => {
        console.log('Python stderr:', data.toString());
        errorOutput += data.toString();
      });

      // Handle process completion
      pythonProcess.on('close', (code) => {
        console.log(`Python process exited with code ${code}`);

        if (code !== 0) {
          console.error('AI Evaluation failed with code:', code);
          console.error('Error output:', errorOutput);
          return reject(new Error(`AI Evaluation failed with code ${code}: ${errorOutput}`));
        }

        try {
          // Parse the result
          const evaluation = JSON.parse(result);

          if (evaluation.error) {
            console.error('AI Evaluation returned error:', evaluation.error);
            return reject(new Error(evaluation.error));
          }

          resolve(evaluation);
        } catch (parseError) {
          console.error('Failed to parse AI evaluation result:', parseError);
          console.error('Raw result:', result);
          reject(new Error(`Failed to parse AI evaluation result: ${parseError.message}`));
        }
      });

      // Handle process error
      pythonProcess.on('error', (error) => {
        console.error('Failed to start Python process:', error);
        reject(new Error(`Failed to start Python process: ${error.message}`));
      });

    } catch (error) {
      console.error('Error in generateAIEvaluation:', error);
      reject(error);
    }
  });
};

/**
 * Create an AI evaluation for a user
 * @param {string} userId - User ID
 * @param {string} evaluatorId - HR user ID
 * @returns {Promise<Object>} - Created evaluation
 */
const createAIEvaluation = async (userId, evaluatorId) => {
  try {
    console.log('Creating AI evaluation for user:', userId);
    console.log('Evaluator ID:', evaluatorId);

    // Get user data
    const user = await User.findById(userId);
    if (!user) {
      throw new Error('User not found');
    }

    console.log('User found:', user.name);

    // Get user's tasks
    const tasks = await Task.find({
      assignedTo: userId,
      createdAt: { $gte: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000) } // Last 90 days
    }).populate('createdBy', 'name');

    console.log(`Found ${tasks.length} tasks for user`);

    // Get user's attendance records
    const attendanceRecords = await Attendance.find({
      userId,
      date: { $gte: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] } // Last 90 days
    });

    console.log(`Found ${attendanceRecords.length} attendance records for user`);

    // Get user's previous evaluations
    const previousEvaluations = await Evaluation.find({
      userId,
      evaluationDate: { $gte: new Date(Date.now() - 180 * 24 * 60 * 60 * 1000) } // Last 180 days
    });

    console.log(`Found ${previousEvaluations.length} previous evaluations for user`);

    // Generate a highly accurate AI evaluation focused exclusively on tasks and attendance

    // ==================== TASK PERFORMANCE ANALYSIS ====================
    // Calculate performance rating based on task completion and timeliness
    let taskPerformanceRating = 0; // Will be calculated on a 1-5 scale
    let completedTasks = 0;
    let onTimeTasks = 0;
    let lateCompletedTasks = 0;
    let highPriorityCompleted = 0;
    let highPriorityTotal = 0;
    let taskQualityScore = 0;
    let taskQualityCount = 0;

    // Count tasks by status and priority
    tasks.forEach(task => {
      // Track high priority tasks
      if (task.priority === 'High' || task.priority === 'Urgent') {
        highPriorityTotal++;
        if (task.status === 'Completed') {
          highPriorityCompleted++;
        }
      }

      // Track completed tasks and their timeliness
      if (task.status === 'Completed') {
        completedTasks++;

        // Check if completed on time
        if (task.completedAt && task.deadline) {
          const completedDate = new Date(task.completedAt);
          const deadlineDate = new Date(task.deadline);

          if (completedDate <= deadlineDate) {
            onTimeTasks++;
          } else {
            // Calculate how late the task was completed (in days)
            const daysLate = Math.ceil((completedDate - deadlineDate) / (1000 * 60 * 60 * 24));
            lateCompletedTasks++;

            // More penalty for being very late
            if (daysLate > 7) {
              taskQualityScore -= 0.5; // Significant lateness
            } else {
              taskQualityScore -= 0.2; // Minor lateness
            }
            taskQualityCount++;
          }
        }

        // Check for quality indicators in feedback
        if (task.feedback) {
          const feedback = task.feedback.toLowerCase();

          // Check for positive quality indicators
          if (feedback.includes('excellent') || feedback.includes('outstanding')) {
            taskQualityScore += 1.0;
            taskQualityCount++;
          } else if (feedback.includes('good') || feedback.includes('well done')) {
            taskQualityScore += 0.5;
            taskQualityCount++;
          } else if (feedback.includes('satisfactory') || feedback.includes('acceptable')) {
            taskQualityScore += 0.2;
            taskQualityCount++;
          }

          // Check for negative quality indicators
          if (feedback.includes('poor') || feedback.includes('inadequate')) {
            taskQualityScore -= 0.5;
            taskQualityCount++;
          } else if (feedback.includes('needs improvement') || feedback.includes('revise')) {
            taskQualityScore -= 0.3;
            taskQualityCount++;
          }
        }
      }
    });

    // Calculate task performance metrics
    if (tasks.length > 0) {
      // Base completion rate (30% of score)
      const completionRate = completedTasks / tasks.length;
      let completionScore = completionRate * 5; // Scale to 1-5

      // Timeliness rate (30% of score)
      const timelinessRate = completedTasks > 0 ? onTimeTasks / completedTasks : 0;
      let timelinessScore = timelinessRate * 5; // Scale to 1-5

      // High priority task handling (20% of score)
      let priorityScore = 3.0; // Default
      if (highPriorityTotal > 0) {
        priorityScore = (highPriorityCompleted / highPriorityTotal) * 5;
      }

      // Quality of work (20% of score)
      let qualityScore = 3.0; // Default
      if (taskQualityCount > 0) {
        qualityScore = 3.0 + (taskQualityScore / taskQualityCount);
        qualityScore = Math.min(Math.max(qualityScore, 1.0), 5.0);
      }

      // Calculate weighted task performance rating
      taskPerformanceRating = (completionScore * 0.3) + (timelinessScore * 0.3) +
                             (priorityScore * 0.2) + (qualityScore * 0.2);

      // Ensure rating is within 1-5 range
      taskPerformanceRating = Math.min(Math.max(taskPerformanceRating, 1.0), 5.0);
    } else {
      // No tasks to evaluate
      taskPerformanceRating = 3.0; // Neutral default
    }

    // ==================== ATTENDANCE ANALYSIS ====================
    // Calculate attendance rating based on presence, punctuality, and patterns
    let attendanceRating = 0; // Will be calculated on a 1-5 scale
    let presentCount = 0;
    let lateCount = 0;
    let absentCount = 0;
    let excusedAbsenceCount = 0;
    let consecutiveAbsences = 0;
    let maxConsecutiveAbsences = 0;
    let patternViolations = 0; // For detecting suspicious patterns

    // Sort attendance records by date for pattern analysis
    const sortedRecords = [...attendanceRecords].sort((a, b) =>
      new Date(a.date) - new Date(b.date)
    );

    // Analyze attendance records
    sortedRecords.forEach((record, index) => {
      if (record.status === 'Present') {
        presentCount++;
        consecutiveAbsences = 0; // Reset consecutive absences
      } else if (record.status === 'Late') {
        lateCount++;
        consecutiveAbsences = 0; // Reset consecutive absences

        // Check how late (if minutes are recorded)
        if (record.minutesLate) {
          if (record.minutesLate > 60) {
            patternViolations += 0.5; // Significantly late
          } else if (record.minutesLate > 30) {
            patternViolations += 0.2; // Moderately late
          }
        }
      } else if (record.status === 'Absent') {
        absentCount++;
        consecutiveAbsences++;
        maxConsecutiveAbsences = Math.max(maxConsecutiveAbsences, consecutiveAbsences);

        // Check if absence is excused
        if (record.excused) {
          excusedAbsenceCount++;
        } else {
          patternViolations += 0.3; // Unexcused absence
        }
      }

      // Check for suspicious patterns (e.g., absent on Mondays/Fridays)
      if (index > 0 && record.date) {
        const date = new Date(record.date);
        const dayOfWeek = date.getDay();

        // Check for frequent absences on Mondays (0) or Fridays (4)
        if ((dayOfWeek === 0 || dayOfWeek === 4) && record.status === 'Absent') {
          // Look for patterns of Monday/Friday absences
          let patternCount = 0;
          for (let i = Math.max(0, index - 10); i < index; i++) {
            const prevDate = new Date(sortedRecords[i].date);
            const prevDay = prevDate.getDay();
            if ((prevDay === 0 || prevDay === 4) && sortedRecords[i].status === 'Absent') {
              patternCount++;
            }
          }

          if (patternCount >= 2) {
            patternViolations += 0.5; // Suspicious pattern detected
          }
        }
      }
    });

    // Calculate attendance metrics
    if (attendanceRecords.length > 0) {
      // Presence rate (40% of score)
      const presentRate = presentCount / attendanceRecords.length;
      let presenceScore = presentRate * 5; // Scale to 1-5

      // Punctuality (30% of score)
      const punctualityRate = 1 - (lateCount / (presentCount + lateCount || 1));
      let punctualityScore = punctualityRate * 5; // Scale to 1-5

      // Absence management (20% of score)
      let absenceScore = 5.0;
      if (absentCount > 0) {
        // Calculate excused absence rate
        const excusedRate = excusedAbsenceCount / absentCount;
        // Penalize based on unexcused absences and consecutive absences
        absenceScore = 5.0 - ((1 - excusedRate) * 2.5) - (maxConsecutiveAbsences * 0.5);
        absenceScore = Math.min(Math.max(absenceScore, 1.0), 5.0);
      }

      // Pattern analysis (10% of score)
      let patternScore = 5.0 - patternViolations;
      patternScore = Math.min(Math.max(patternScore, 1.0), 5.0);

      // Calculate weighted attendance rating
      attendanceRating = (presenceScore * 0.4) + (punctualityScore * 0.3) +
                         (absenceScore * 0.2) + (patternScore * 0.1);

      // Ensure rating is within 1-5 range
      attendanceRating = Math.min(Math.max(attendanceRating, 1.0), 5.0);
    } else {
      // No attendance records to evaluate
      attendanceRating = 3.0; // Neutral default
    }

    // ==================== COMBINED PERFORMANCE RATING ====================
    // Calculate overall performance rating (60% tasks, 40% attendance)
    const performanceRating = (taskPerformanceRating * 0.6) + (attendanceRating * 0.4);

    // ==================== GENERATE INSIGHTS ====================
    // Generate detailed insights for tasks
    const taskInsights = {
      performance: generateDetailedTaskPerformanceInsight(
        tasks,
        completedTasks,
        onTimeTasks,
        lateCompletedTasks,
        highPriorityCompleted,
        highPriorityTotal,
        taskPerformanceRating
      )
    };

    // Generate detailed insights for attendance
    const attendanceInsight = generateDetailedAttendanceInsight(
      attendanceRecords,
      presentCount,
      lateCount,
      absentCount,
      excusedAbsenceCount,
      maxConsecutiveAbsences,
      patternViolations,
      attendanceRating
    );

    // Generate strengths based only on task and attendance performance
    const strengths = generateTaskAttendanceStrengths(taskPerformanceRating, attendanceRating);

    // Generate areas for improvement based only on task and attendance performance
    const areasForImprovement = generateTaskAttendanceImprovements(taskPerformanceRating, attendanceRating);

    // Generate goals based only on task and attendance performance
    const goals = generateTaskAttendanceGoals(taskPerformanceRating, attendanceRating);

    // Create evaluation in database with calculated values
    const evaluation = new Evaluation({
      userId,
      evaluatorId,
      evaluationPeriod: 'Monthly',
      evaluationDate: new Date(),
      performanceRating: parseFloat(performanceRating.toFixed(1)),
      // Set other ratings to null to indicate they should be manually evaluated
      attitudeRating: null,
      communicationRating: null,
      teamworkRating: null,
      initiativeRating: null,
      strengths,
      areasForImprovement,
      goals,
      comments: `This AI-generated evaluation is based on ${tasks.length} tasks and ${attendanceRecords.length} attendance records from the past 90 days. The evaluation focuses exclusively on task performance (${taskPerformanceRating.toFixed(1)}/5.0) and attendance (${attendanceRating.toFixed(1)}/5.0). Other criteria should be evaluated manually.`,
      status: 'Draft',
      aiGenerated: true,
      visibleToUser: false, // AI evaluations are not visible to users by default
      insights: {
        tasks: taskInsights,
        attendance: attendanceInsight
      }
    });

    await evaluation.save();
    console.log('AI evaluation saved to database with ID:', evaluation._id);

    return evaluation;

    /* Commented out until Python environment is properly set up
    // Get user's tasks
    const tasks = await Task.find({
      assignedTo: userId,
      createdAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) } // Last 30 days
    }).populate('createdBy', 'name');

    console.log(`Found ${tasks.length} tasks for user`);

    // Get user's attendance records
    const attendanceRecords = await Attendance.find({
      userId,
      date: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] } // Last 30 days
    });

    console.log(`Found ${attendanceRecords.length} attendance records for user`);

    // Get user's previous evaluations
    const previousEvaluations = await Evaluation.find({
      userId,
      evaluationDate: { $gte: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000) } // Last 90 days
    });

    console.log(`Found ${previousEvaluations.length} previous evaluations for user`);

    // Generate AI evaluation
    const aiEvaluation = await generateAIEvaluation(
      user.toObject(),
      tasks.map(t => t.toObject()),
      attendanceRecords.map(a => a.toObject()),
      previousEvaluations.map(e => e.toObject())
    );

    console.log('AI evaluation generated successfully');

    // Create evaluation in database
    const evaluation = new Evaluation({
      userId,
      evaluatorId,
      evaluationPeriod: aiEvaluation.evaluationPeriod,
      evaluationDate: new Date(),
      performanceRating: aiEvaluation.performanceRating,
      attitudeRating: aiEvaluation.attitudeRating,
      communicationRating: aiEvaluation.communicationRating,
      teamworkRating: aiEvaluation.teamworkRating,
      initiativeRating: aiEvaluation.initiativeRating,
      strengths: aiEvaluation.strengths,
      areasForImprovement: aiEvaluation.areasForImprovement,
      goals: aiEvaluation.goals,
      comments: aiEvaluation.comments,
      status: 'Draft',
      aiGenerated: true,
      insights: aiEvaluation.insights
    });

    await evaluation.save();
    console.log('AI evaluation saved to database');
    */

    return evaluation;
  } catch (error) {
    console.error('Error in createAIEvaluation:', error);
    throw error;
  }
};

module.exports = {
  generateAIEvaluation,
  createAIEvaluation
};
