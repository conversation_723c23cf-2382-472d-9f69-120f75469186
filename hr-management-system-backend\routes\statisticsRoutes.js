const express = require('express');
const { authenticate, authorizeRoles } = require('../middleware/authmiddleware');
const User = require('../models/User');
const Application = require('../models/application'); // Fix potential case sensitivity issues
const LeaveRequest = require('../models/LeaveRequest');
const Job = require('../models/job');
const Task = require('../models/Task');
const AuditLog = require('../models/AuditLog');
const LoginHistory = require('../models/LoginHistory');
const Attendance = require('../models/Attendance');
const router = express.Router();

// Helper function to create test data if none exists
const createTestDataIfNeeded = async () => {
  try {
    // Check if we have any applications
    const appCount = await Application.countDocuments();
    if (appCount === 0) {
      console.log('No applications found, creating test data');

      // Get some job IDs to use
      const jobs = await Job.find().limit(3);
      const jobIds = jobs.map(job => job._id);

      // If no jobs exist, create a dummy job ID
      if (jobIds.length === 0) {
        jobIds.push('dummy-job-id');
      }

      // Create test applications
      const testApplications = [
        {
          candidateName: 'John <PERSON>',
          email: '<EMAIL>',
          phone: '555-1234',
          jobId: jobIds[0] || 'dummy-job-id',
          status: 'Approved',
          createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // 7 days ago
        },
        {
          candidateName: 'Jane Doe',
          email: '<EMAIL>',
          phone: '555-5678',
          jobId: jobIds[0] || 'dummy-job-id',
          status: 'Pending',
          createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000) // 3 days ago
        },
        {
          candidateName: 'Bob Johnson',
          email: '<EMAIL>',
          phone: '555-9012',
          jobId: jobIds[1] || 'dummy-job-id',
          status: 'Rejected',
          createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000) // 5 days ago
        }
      ];

      await Application.insertMany(testApplications);
      console.log('Created test applications');
    }

    // Check if we have any leave requests
    const leaveCount = await LeaveRequest.countDocuments();
    if (leaveCount === 0) {
      console.log('No leave requests found, creating test data');

      // Get some user IDs to use
      const users = await User.find().limit(3);
      const userIds = users.map(user => user._id);

      // If no users exist, create a dummy user ID
      if (userIds.length === 0) {
        userIds.push('dummy-user-id');
      }

      // Create test leave requests
      const testLeaveRequests = [
        {
          userId: userIds[0] || 'dummy-user-id',
          leaveType: 'Vacation',
          startDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
          endDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // 14 days from now
          reason: 'Annual vacation',
          status: 'Approved',
          createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000) // 10 days ago
        },
        {
          userId: userIds[1] || 'dummy-user-id',
          leaveType: 'Sick',
          startDate: new Date(Date.now() + 1 * 24 * 60 * 60 * 1000), // 1 day from now
          endDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000), // 3 days from now
          reason: 'Not feeling well',
          status: 'Pending',
          createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000) // 2 days ago
        },
        {
          userId: userIds[2] || 'dummy-user-id',
          leaveType: 'Personal',
          startDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000), // 5 days from now
          endDate: new Date(Date.now() + 6 * 24 * 60 * 60 * 1000), // 6 days from now
          reason: 'Family event',
          status: 'Rejected',
          createdAt: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000) // 4 days ago
        }
      ];

      await LeaveRequest.insertMany(testLeaveRequests);
      console.log('Created test leave requests');
    }
  } catch (error) {
    console.error('Error creating test data:', error);
  }
};

// Get user statistics
router.get('/user-statistics', authenticate, authorizeRoles('admin'), async (req, res) => {
  try {
    // Get total users
    const totalUsers = await User.countDocuments();

    // Get users by role
    const usersByRole = await User.aggregate([
      { $group: { _id: '$role', count: { $sum: 1 } } },
      { $project: { role: '$_id', count: 1, _id: 0 } },
      { $sort: { count: -1 } }
    ]);

    // Get users by active status
    const usersByStatus = await User.aggregate([
      {
        $group: {
          _id: { $cond: [{ $eq: ['$active', false] }, 'inactive', 'active'] },
          count: { $sum: 1 }
        }
      },
      { $project: { status: '$_id', count: 1, _id: 0 } },
      { $sort: { count: -1 } }
    ]);

    // Get recently created users
    const recentUsers = await User.find()
      .sort({ creationDate: -1 })
      .limit(5)
      .select('-password');

    res.status(200).json({
      totalUsers,
      usersByRole,
      usersByStatus,
      recentUsers
    });
  } catch (error) {
    console.error('Error fetching user statistics:', error);
    res.status(500).json({ message: 'Server error while fetching user statistics' });
  }
});

// Get application statistics
router.get('/application-statistics', authenticate, authorizeRoles('admin', 'hr'), async (req, res) => {
  try {
    // Create test data if needed
    await createTestDataIfNeeded();

    const { startDate, endDate } = req.query;
    console.log('Application statistics request received with date range:', { startDate, endDate });

    // Build date filter - use createdAt instead of applicationDate since that's what the model has
    const dateFilter = {};
    if (startDate || endDate) {
      dateFilter.createdAt = {};

      if (startDate) {
        dateFilter.createdAt.$gte = new Date(startDate);
      }

      if (endDate) {
        // Add one day to include the end date fully
        const endDateObj = new Date(endDate);
        endDateObj.setDate(endDateObj.getDate() + 1);
        dateFilter.createdAt.$lte = endDateObj;
      }
    }

    console.log('Using date filter:', JSON.stringify(dateFilter));

    // Get total applications
    const totalApplications = await Application.countDocuments(dateFilter);

    // Get applications by status
    const approvedApplications = await Application.countDocuments({
      ...dateFilter,
      status: 'Approved'
    });

    const rejectedApplications = await Application.countDocuments({
      ...dateFilter,
      status: 'Rejected'
    });

    const pendingApplications = await Application.countDocuments({
      ...dateFilter,
      status: 'Pending'
    });

    // Get applications by job
    const applicationsByJob = await Application.aggregate([
      { $match: dateFilter },
      { $group: { _id: '$jobId', count: { $sum: 1 } } },
      { $sort: { count: -1 } },
      { $limit: 10 },
      {
        $lookup: {
          from: 'jobs',
          localField: '_id',
          foreignField: '_id',
          as: 'job'
        }
      },
      {
        $project: {
          jobId: '$_id',
          jobTitle: { $arrayElemAt: ['$job.title', 0] },
          count: 1,
          _id: 0
        }
      }
    ]);

    // Get applications by day - use createdAt instead of applicationDate
    const applicationsByDay = await Application.aggregate([
      { $match: dateFilter },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' },
            day: { $dayOfMonth: '$createdAt' }
          },
          count: { $sum: 1 }
        }
      },
      {
        $project: {
          _id: 0,
          date: {
            $dateFromParts: {
              year: '$_id.year',
              month: '$_id.month',
              day: '$_id.day'
            }
          },
          count: 1
        }
      },
      { $sort: { date: 1 } }
    ]);

    // Log the results for debugging
    console.log('Application statistics results:', {
      totalApplications,
      approvedApplications,
      rejectedApplications,
      pendingApplications,
      applicationsByJobCount: applicationsByJob.length,
      applicationsByDayCount: applicationsByDay.length
    });

    // Log all applications for debugging
    const allApplications = await Application.find().select('status createdAt');
    console.log('All applications:', allApplications);

    const response = {
      totalApplications,
      approvedApplications,
      rejectedApplications,
      pendingApplications,
      applicationsByJob,
      applicationsByDay
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Error fetching application statistics:', error);
    console.error('Error details:', error.stack);
    res.status(500).json({ message: 'Server error while fetching application statistics' });
  }
});

// Get leave request statistics
router.get('/leave-statistics', authenticate, authorizeRoles('admin', 'hr'), async (req, res) => {
  try {
    // Create test data if needed
    await createTestDataIfNeeded();

    const { startDate, endDate } = req.query;
    console.log('Leave statistics request received with date range:', { startDate, endDate });

    // Build date filter - use createdAt instead of requestDate
    const dateFilter = {};
    if (startDate || endDate) {
      dateFilter.createdAt = {};

      if (startDate) {
        dateFilter.createdAt.$gte = new Date(startDate);
      }

      if (endDate) {
        // Add one day to include the end date fully
        const endDateObj = new Date(endDate);
        endDateObj.setDate(endDateObj.getDate() + 1);
        dateFilter.createdAt.$lte = endDateObj;
      }
    }

    console.log('Using leave date filter:', JSON.stringify(dateFilter));

    // Get total leave requests
    const totalRequests = await LeaveRequest.countDocuments(dateFilter);

    // Get leave requests by status
    const approvedRequests = await LeaveRequest.countDocuments({
      ...dateFilter,
      status: 'Approved'
    });

    const rejectedRequests = await LeaveRequest.countDocuments({
      ...dateFilter,
      status: 'Rejected'
    });

    const pendingRequests = await LeaveRequest.countDocuments({
      ...dateFilter,
      status: 'Pending'
    });

    // Get leave requests by type
    const requestsByType = await LeaveRequest.aggregate([
      { $match: dateFilter },
      { $group: { _id: '$leaveType', count: { $sum: 1 } } },
      { $project: { type: '$_id', count: 1, _id: 0 } },
      { $sort: { count: -1 } }
    ]);

    // Get leave requests by user
    const requestsByUser = await LeaveRequest.aggregate([
      { $match: dateFilter },
      { $group: { _id: '$userId', count: { $sum: 1 } } },
      { $sort: { count: -1 } },
      { $limit: 10 },
      {
        $lookup: {
          from: 'users',
          localField: '_id',
          foreignField: '_id',
          as: 'user'
        }
      },
      {
        $project: {
          userId: '$_id',
          userName: { $arrayElemAt: ['$user.name', 0] },
          count: 1,
          _id: 0
        }
      }
    ]);

    // Get leave requests by day - use createdAt instead of requestDate
    const requestsByDay = await LeaveRequest.aggregate([
      { $match: dateFilter },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' },
            day: { $dayOfMonth: '$createdAt' }
          },
          count: { $sum: 1 }
        }
      },
      {
        $project: {
          _id: 0,
          date: {
            $dateFromParts: {
              year: '$_id.year',
              month: '$_id.month',
              day: '$_id.day'
            }
          },
          count: 1
        }
      },
      { $sort: { date: 1 } }
    ]);

    // Log the results for debugging
    console.log('Leave statistics results:', {
      totalRequests,
      approvedRequests,
      rejectedRequests,
      pendingRequests,
      requestsByTypeCount: requestsByType.length,
      requestsByUserCount: requestsByUser.length,
      requestsByDayCount: requestsByDay.length
    });

    // Log all leave requests for debugging
    const allLeaveRequests = await LeaveRequest.find().select('status leaveType createdAt');
    console.log('All leave requests:', allLeaveRequests);

    const response = {
      totalRequests,
      approvedRequests,
      rejectedRequests,
      pendingRequests,
      requestsByType,
      requestsByUser,
      requestsByDay
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Error fetching leave statistics:', error);
    console.error('Error details:', error.stack);
    res.status(500).json({ message: 'Server error while fetching leave statistics' });
  }
});

// Get task statistics
router.get('/task-statistics', authenticate, authorizeRoles('admin', 'hr'), async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    // Build date filter
    const dateFilter = {};
    if (startDate || endDate) {
      dateFilter.createdAt = {};

      if (startDate) {
        dateFilter.createdAt.$gte = new Date(startDate);
      }

      if (endDate) {
        // Add one day to include the end date fully
        const endDateObj = new Date(endDate);
        endDateObj.setDate(endDateObj.getDate() + 1);
        dateFilter.createdAt.$lte = endDateObj;
      }
    }

    // Get total tasks
    const totalTasks = await Task.countDocuments(dateFilter);

    // Get tasks by status
    const completedTasks = await Task.countDocuments({
      ...dateFilter,
      status: 'completed'
    });

    const inProgressTasks = await Task.countDocuments({
      ...dateFilter,
      status: 'in-progress'
    });

    const pendingTasks = await Task.countDocuments({
      ...dateFilter,
      status: 'pending'
    });

    // Get tasks by department
    const tasksByDepartment = await Task.aggregate([
      { $match: dateFilter },
      { $group: { _id: '$department', count: { $sum: 1 } } },
      { $project: { department: '$_id', count: 1, _id: 0 } },
      { $sort: { count: -1 } }
    ]);

    // Get tasks by assignee
    const tasksByAssignee = await Task.aggregate([
      { $match: dateFilter },
      { $group: { _id: '$assignedTo', count: { $sum: 1 } } },
      { $sort: { count: -1 } },
      { $limit: 10 },
      {
        $lookup: {
          from: 'users',
          localField: '_id',
          foreignField: '_id',
          as: 'user'
        }
      },
      {
        $project: {
          userId: '$_id',
          userName: { $arrayElemAt: ['$user.name', 0] },
          count: 1,
          _id: 0
        }
      }
    ]);

    res.status(200).json({
      totalTasks,
      completedTasks,
      inProgressTasks,
      pendingTasks,
      tasksByDepartment,
      tasksByAssignee
    });
  } catch (error) {
    console.error('Error fetching task statistics:', error);
    res.status(500).json({ message: 'Server error while fetching task statistics' });
  }
});

// Get login statistics
router.get('/login-statistics', authenticate, authorizeRoles('admin'), async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    // Date range filter
    const dateFilter = {};
    if (startDate || endDate) {
      dateFilter.loginTime = {};

      if (startDate) {
        dateFilter.loginTime.$gte = new Date(startDate);
      }

      if (endDate) {
        // Add one day to include the end date fully
        const endDateObj = new Date(endDate);
        endDateObj.setDate(endDateObj.getDate() + 1);
        dateFilter.loginTime.$lte = endDateObj;
      }
    }

    console.log('Fetching login statistics with filter:', JSON.stringify(dateFilter));

    // Get total login attempts
    const totalLogins = await LoginHistory.countDocuments(dateFilter);
    console.log('Total logins:', totalLogins);

    // Get successful logins
    const successfulLogins = await LoginHistory.countDocuments({
      ...dateFilter,
      status: 'SUCCESS'
    });
    console.log('Successful logins:', successfulLogins);

    // Get failed logins
    const failedLogins = await LoginHistory.countDocuments({
      ...dateFilter,
      status: 'FAILED'
    });
    console.log('Failed logins:', failedLogins);

    // Get unique users who logged in
    const uniqueUsers = await LoginHistory.distinct('userId', {
      ...dateFilter,
      status: 'SUCCESS'
    });
    console.log('Unique users count:', uniqueUsers.length);

    // Get unique IP addresses
    const uniqueIPs = await LoginHistory.distinct('ipAddress', dateFilter);
    console.log('Unique IPs count:', uniqueIPs.length);

    // Get login counts by role
    const loginsByRole = await LoginHistory.aggregate([
      { $match: { ...dateFilter, status: 'SUCCESS' } },
      { $group: { _id: '$userInfo.role', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);
    console.log('Logins by role:', loginsByRole);

    // Get login counts by browser
    const loginsByBrowser = await LoginHistory.aggregate([
      { $match: dateFilter },
      { $group: { _id: '$browser.name', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);
    console.log('Logins by browser:', loginsByBrowser);

    // Get login counts by device
    const loginsByDevice = await LoginHistory.aggregate([
      { $match: dateFilter },
      { $group: { _id: '$device.type', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);
    console.log('Logins by device:', loginsByDevice);

    // Get login counts by OS
    const loginsByOS = await LoginHistory.aggregate([
      { $match: dateFilter },
      { $group: { _id: '$os.name', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);
    console.log('Logins by OS:', loginsByOS);

    // Get logins by day
    const loginsByDay = await LoginHistory.aggregate([
      { $match: dateFilter },
      {
        $group: {
          _id: {
            year: { $year: '$loginTime' },
            month: { $month: '$loginTime' },
            day: { $dayOfMonth: '$loginTime' }
          },
          total: { $sum: 1 },
          successful: {
            $sum: {
              $cond: [{ $eq: ['$status', 'SUCCESS'] }, 1, 0]
            }
          },
          failed: {
            $sum: {
              $cond: [{ $eq: ['$status', 'FAILED'] }, 1, 0]
            }
          }
        }
      },
      {
        $project: {
          _id: 0,
          date: {
            $dateFromParts: {
              year: '$_id.year',
              month: '$_id.month',
              day: '$_id.day'
            }
          },
          total: 1,
          successful: 1,
          failed: 1
        }
      },
      { $sort: { date: 1 } }
    ]);
    console.log('Logins by day:', loginsByDay);

    res.status(200).json({
      summary: {
        totalLogins,
        successfulLogins,
        failedLogins,
        uniqueUserCount: uniqueUsers.length,
        uniqueIPCount: uniqueIPs.length,
        successRate: totalLogins > 0 ? (successfulLogins / totalLogins) * 100 : 0
      },
      loginsByRole: loginsByRole.map(item => ({
        role: item._id || 'Unknown',
        count: item.count
      })),
      loginsByBrowser: loginsByBrowser.map(item => ({
        browser: item._id || 'Unknown',
        count: item.count
      })),
      loginsByDevice: loginsByDevice.map(item => ({
        device: item._id || 'Unknown',
        count: item.count
      })),
      loginsByOS: loginsByOS.map(item => ({
        os: item._id || 'Unknown',
        count: item.count
      })),
      loginsByDay
    });
  } catch (error) {
    console.error('Error fetching login statistics:', error);
    res.status(500).json({ message: 'Server error while fetching login statistics' });
  }
});

// Get job statistics
router.get('/job-statistics', authenticate, authorizeRoles('admin', 'hr'), async (req, res) => {
  try {
    // Get total jobs
    const totalJobs = await Job.countDocuments();

    // Get active jobs
    const activeJobs = await Job.countDocuments({ active: true });

    // Get jobs by department
    const jobsByDepartment = await Job.aggregate([
      { $group: { _id: '$department', count: { $sum: 1 } } },
      { $project: { department: '$_id', count: 1, _id: 0 } },
      { $sort: { count: -1 } }
    ]);

    // Get recent jobs
    const recentJobs = await Job.find()
      .sort({ createdAt: -1 })
      .limit(5);

    res.status(200).json({
      totalJobs,
      activeJobs,
      jobsByDepartment,
      recentJobs
    });
  } catch (error) {
    console.error('Error fetching job statistics:', error);
    res.status(500).json({ message: 'Server error while fetching job statistics' });
  }
});

// Generate report
router.post('/reports/generate', authenticate, authorizeRoles('admin'), async (req, res) => {
  try {
    const { reportType, dateRange, filters, fields } = req.body;

    // Parse date range
    const startDate = dateRange.startDate ? new Date(dateRange.startDate) : null;
    const endDate = dateRange.endDate ? new Date(dateRange.endDate) : null;

    // Add one day to end date to include the full day
    if (endDate) {
      endDate.setDate(endDate.getDate() + 1);
    }

    // Initialize report data
    let reportData = [];
    let totalRecords = 0;
    let reportSummary = '';

    // Fetch data based on report type
    switch (reportType) {
      case 'users': {
        // Build query
        const query = {};

        // Apply status filter
        if (filters.status && filters.status !== 'all') {
          if (filters.status === 'active') {
            query.active = true;
          } else if (filters.status === 'inactive') {
            query.active = false;
          }
        }

        console.log('User query:', query);

        // Apply role filter
        if (filters.role && filters.role !== 'all') {
          query.role = filters.role;
        }

        // Fetch users
        const users = await User.find(query).select('-password');
        totalRecords = users.length;

        // Debug: Log all users to see their structure
        console.log('All users:', JSON.stringify(users, null, 2));

        // IMPORTANT: We should NOT override existing user data
        // Only add test data if there are no users at all
        if (users.length === 0) {
          console.log('No users found, adding test data');

          // Create test users with departments if none exist
          const testUsers = [
            {
              _id: 'test1',
              name: 'John Doe',
              email: '<EMAIL>',
              role: 'user',
              job: 'Software Engineer',
              department: 'Engineering',
              creationDate: new Date(),
              active: true
            },
            {
              _id: 'test2',
              name: 'Jane Smith',
              email: '<EMAIL>',
              role: 'hr',
              job: 'HR Manager',
              department: 'HR',
              creationDate: new Date(),
              active: true
            },
            {
              _id: 'test3',
              name: 'Bob Johnson',
              email: '<EMAIL>',
              role: 'user',
              job: 'Marketing Specialist',
              department: 'Marketing',
              creationDate: new Date(),
              active: true
            }
          ];

          // Use test data only if there are no users
          users.push(...testUsers);
        } else {
          // Log the actual user data to see what departments they have
          console.log('Existing users with their departments:');
          users.forEach(user => {
            console.log(`User: ${user.name}, Department: ${user.department || 'Not specified'}`);
          });
        }

        // Create a consistent field order
        const fieldOrder = ['name', 'email', 'role', 'department', 'job', 'creationDate', 'lastLogin', 'status'];

        // Filter fields based on selection and maintain order
        reportData = users.map(user => {
          const userData = {};

          // Add fields in the specified order
          fieldOrder.forEach(field => {
            if (fields[field]) {
              switch (field) {
                case 'name':
                  userData.name = user.name || 'Unknown';
                  break;
                case 'email':
                  userData.email = user.email || 'No Email';
                  break;
                case 'role':
                  userData.role = user.role || 'user';
                  break;
                case 'department':
                  // IMPORTANT: Use the actual department from the user data
                  // Log the actual department value for debugging
                  console.log(`User ${user.name} has department: ${user.department || 'Not specified'}`);

                  // Use the actual department value, or 'Not specified' if it's missing
                  userData.department = user.department || 'Not specified';
                  break;
                case 'job':
                  userData.job = user.job || 'Not specified';
                  break;
                case 'creationDate':
                  userData.creationDate = user.creationDate || new Date();
                  break;
                case 'lastLogin':
                  userData.lastLogin = user.lastLogin || 'Never';
                  break;
                case 'status':
                  userData.status = user.active === false ? 'Inactive' : 'Active';
                  break;
              }
            }
          });

          return userData;
        });

        reportSummary = `This report contains information about ${totalRecords} users in the system.`;
        break;
      }

      case 'applications': {
        // Build query
        const query = {};

        // Apply date filter
        if (startDate || endDate) {
          query.createdAt = {};
          if (startDate) query.createdAt.$gte = startDate;
          if (endDate) query.createdAt.$lte = endDate;
        }

        // Apply status filter
        if (filters.status && filters.status !== 'all') {
          query.status = filters.status;
        }

        // Apply job filter
        if (filters.jobId && filters.jobId !== 'all') {
          query.jobId = filters.jobId;
        }

        console.log('Applications query:', query);

        // Fetch applications
        const applications = await Application.find(query).populate('jobId', 'title');
        totalRecords = applications.length;

        // Create a consistent field order
        const fieldOrder = ['applicantName', 'jobTitle', 'applicationDate', 'status', 'cvScore', 'contactInfo'];

        // Filter fields based on selection and maintain order
        reportData = applications.map(app => {
          const appData = {};

          // Add fields in the specified order
          fieldOrder.forEach(field => {
            if (fields[field]) {
              switch (field) {
                case 'applicantName':
                  appData.applicantName = app.fullname || 'Unknown';
                  break;
                case 'jobTitle':
                  appData.jobTitle = app.jobTitle || (app.jobId ? app.jobId.title : 'Unknown');
                  break;
                case 'applicationDate':
                  appData.applicationDate = app.createdAt || new Date();
                  break;
                case 'status':
                  appData.status = app.status || 'Pending';
                  break;
                case 'cvScore':
                  appData.cvScore = app.matchScore || 'Not processed';
                  break;
                case 'contactInfo':
                  appData.contactInfo = `${app.email || 'No email'} | ${app.phone || 'No phone'}`;
                  break;
              }
            }
          });

          return appData;
        });

        reportSummary = `This report contains information about ${totalRecords} job applications ${startDate ? `from ${startDate.toLocaleDateString()}` : ''} ${endDate ? `to ${new Date(endDate).toLocaleDateString()}` : ''}.`;
        break;
      }

      case 'leave_requests': {
        // Build query
        const query = {};

        // Apply date filter
        if (startDate || endDate) {
          query.createdAt = {};
          if (startDate) query.createdAt.$gte = startDate;
          if (endDate) query.createdAt.$lte = endDate;
        }

        // Apply status filter
        if (filters.status && filters.status !== 'all') {
          query.status = filters.status;
        }

        // Apply leave type filter
        if (filters.leaveType && filters.leaveType !== 'all') {
          query.leaveType = filters.leaveType;
        }

        // Apply employee filter
        if (filters.employeeId && filters.employeeId !== 'all') {
          query.userId = filters.employeeId;
        }

        console.log('Leave requests query:', query);

        // Fetch leave requests
        const leaveRequests = await LeaveRequest.find(query);
        totalRecords = leaveRequests.length;

        // Create a consistent field order
        const fieldOrder = ['employeeName', 'leaveType', 'startDate', 'endDate', 'status', 'requestDate', 'approverName'];

        // Filter fields based on selection and maintain order
        reportData = leaveRequests.map(leave => {
          const leaveData = {};

          // Add fields in the specified order
          fieldOrder.forEach(field => {
            if (fields[field]) {
              switch (field) {
                case 'employeeName':
                  leaveData.employeeName = leave.employeeName || 'Unknown';
                  break;
                case 'leaveType':
                  leaveData.leaveType = leave.leaveType || 'Not specified';
                  break;
                case 'startDate':
                  leaveData.startDate = leave.startDate || 'Not specified';
                  break;
                case 'endDate':
                  leaveData.endDate = leave.endDate || 'Not specified';
                  break;
                case 'status':
                  leaveData.status = leave.status || 'Pending';
                  break;
                case 'requestDate':
                  leaveData.requestDate = leave.createdAt || new Date();
                  break;
                case 'approverName':
                  leaveData.approverName = leave.approverName || 'Not approved yet';
                  break;
              }
            }
          });

          return leaveData;
        });

        reportSummary = `This report contains information about ${totalRecords} leave requests ${startDate ? `from ${startDate.toLocaleDateString()}` : ''} ${endDate ? `to ${new Date(endDate).toLocaleDateString()}` : ''}.`;
        break;
      }

      case 'jobs': {
        // Build query
        const query = {};

        // Apply status filter
        if (filters.status && filters.status !== 'all') {
          if (filters.status === 'active') {
            query.active = true;
          } else if (filters.status === 'inactive') {
            query.active = false;
          }
        }

        // Apply department filter
        if (filters.department && filters.department !== 'all') {
          query.department = filters.department;
        }

        // Apply location filter
        if (filters.location && filters.location !== 'all') {
          query.location = filters.location;
        }

        // Fetch jobs
        const jobs = await Job.find(query);
        totalRecords = jobs.length;

        // Debug: Log all jobs to see their structure
        console.log('All jobs:', JSON.stringify(jobs, null, 2));

        // IMPORTANT: We should NOT override existing job data
        // Only add test data if there are no jobs at all
        if (jobs.length === 0) {
          console.log('No jobs found, adding test data');

          // Create test jobs with departments if none exist
          const testJobs = [
            {
              _id: 'job1',
              title: 'Software Engineer',
              department: 'Engineering',
              location: 'Remote',
              createdAt: new Date(),
              closingDate: new Date(new Date().setMonth(new Date().getMonth() + 1)),
              active: true
            },
            {
              _id: 'job2',
              title: 'HR Manager',
              department: 'HR',
              location: 'Onsite',
              createdAt: new Date(),
              closingDate: null,
              active: true
            },
            {
              _id: 'job3',
              title: 'Marketing Specialist',
              department: 'Marketing',
              location: 'Hybrid',
              createdAt: new Date(),
              closingDate: new Date(new Date().setMonth(new Date().getMonth() + 2)),
              active: true
            }
          ];

          // Use test data only if there are no jobs
          jobs.push(...testJobs);
        } else {
          // Log the actual job data to see what departments they have
          console.log('Existing jobs with their departments:');
          jobs.forEach(job => {
            console.log(`Job: ${job.title}, Department: ${job.department || 'Not specified'}`);
          });
        }

        // Get application counts for each job
        const applicationCounts = await Promise.all(
          jobs.map(async (job) => {
            const count = await Application.countDocuments({ jobId: job._id });
            return { jobId: job._id, count };
          })
        );

        // Create a map for quick lookup
        const applicationCountMap = applicationCounts.reduce((map, item) => {
          map[item.jobId.toString()] = item.count;
          return map;
        }, {});

        // Create a consistent field order
        const fieldOrder = ['title', 'department', 'location', 'postDate', 'closingDate', 'status', 'applicantCount'];

        // Filter fields based on selection and maintain order
        reportData = jobs.map(job => {
          const jobData = {};

          // Add fields in the specified order
          fieldOrder.forEach(field => {
            if (fields[field]) {
              switch (field) {
                case 'title':
                  jobData.title = job.title || 'Untitled';
                  break;
                case 'department':
                  // IMPORTANT: Use the actual department from the job data
                  // Log the actual department value for debugging
                  console.log(`Job ${job.title} has department: ${job.department || 'Not specified'}`);

                  // Use the actual department value, or 'Not specified' if it's missing
                  jobData.department = job.department || 'Not specified';
                  break;
                case 'location':
                  jobData.location = job.location || 'Not specified';
                  break;
                case 'postDate':
                  jobData.postDate = job.createdAt || new Date();
                  break;
                case 'closingDate':
                  jobData.closingDate = job.closingDate || 'Open';
                  break;
                case 'status':
                  jobData.status = job.active ? 'Active' : 'Inactive';
                  break;
                case 'applicantCount':
                  jobData.applicantCount = applicationCountMap[job._id.toString()] || 0;
                  break;
              }
            }
          });

          return jobData;
        });

        reportSummary = `This report contains information about ${totalRecords} job postings.`;
        break;
      }

      case 'attendance': {
        // Build query for attendance records
        const query = {};
        let userQuery = {};

        // Apply date filter - convert to string format YYYY-MM-DD
        if (startDate || endDate) {
          if (startDate) {
            const startDateStr = startDate.toISOString().split('T')[0];
            query.date = { $gte: startDateStr };
          }
          if (endDate) {
            const endDateStr = endDate.toISOString().split('T')[0];
            if (query.date) {
              query.date.$lte = endDateStr;
            } else {
              query.date = { $lte: endDateStr };
            }
          }
        }

        // Apply status filter
        if (filters.status && filters.status !== 'all') {
          query.status = filters.status;
        }

        // Apply employee filter
        if (filters.employeeId && filters.employeeId !== 'all') {
          query.userId = filters.employeeId;
        }

        // For department filter, we need to find users in that department first
        if (filters.department && filters.department !== 'all') {
          // Find all users in the specified department
          const usersInDepartment = await User.find({ department: filters.department }).select('_id');
          const userIds = usersInDepartment.map(user => user._id);

          // Add these user IDs to our query
          query.userId = { $in: userIds };
        }

        console.log('Attendance query:', query);

        // Fetch attendance records from the database
        const attendanceQuery = await Attendance.find(query)
          .populate('userId', 'name email department')
          .sort({ date: -1 });

        totalRecords = attendanceQuery.length;

        // Debug: Log the first attendance record to see its structure
        if (attendanceQuery.length > 0) {
          console.log('First attendance record:', JSON.stringify(attendanceQuery[0], null, 2));
          console.log('User details:', JSON.stringify(attendanceQuery[0].userId, null, 2));
        }

        // Map the attendance records to the format we need
        const attendanceRecords = await Promise.all(attendanceQuery.map(async (record) => {
          // Get user details
          const user = record.userId;

          // Debug: Log user details
          if (user) {
            console.log(`User for attendance record: ${user._id}`, JSON.stringify(user, null, 2));
          }

          // IMPORTANT: Use the actual department from the user data
          // Log the actual department value for debugging
          if (user) {
            console.log(`User ${user.name} in attendance record has department: ${user.department || 'Not specified'}`);
          }

          // Use the actual department value, or 'Not specified' if it's missing
          const department = user && user.department ? user.department : 'Not specified';

          return {
            employeeName: user ? user.name : 'Unknown',
            date: record.date,
            checkInTime: record.checkIn,
            checkOutTime: record.checkOut,
            totalHours: record.hoursWorked,
            status: record.status,
            department: department,
            notes: record.notes
          };
        }));

        // Create a consistent field order
        const fieldOrder = ['employeeName', 'department', 'date', 'checkInTime', 'checkOutTime', 'totalHours', 'status'];

        // Filter fields based on selection and maintain order
        reportData = attendanceRecords.map(record => {
          const attendanceData = {};

          // Add fields in the specified order
          fieldOrder.forEach(field => {
            if (fields[field]) {
              switch (field) {
                case 'employeeName':
                  attendanceData.employeeName = record.employeeName || 'Unknown';
                  break;
                case 'department':
                  // IMPORTANT: Use the actual department from the record data
                  // Log the actual department value for debugging
                  console.log(`Attendance record for ${record.employeeName} has department: ${record.department || 'Not specified'}`);

                  // Use the actual department value, or 'Not specified' if it's missing
                  attendanceData.department = record.department || 'Not specified';
                  break;
                case 'date':
                  attendanceData.date = record.date || 'N/A';
                  break;
                case 'checkInTime':
                  attendanceData.checkInTime = record.checkInTime ? new Date(record.checkInTime).toLocaleTimeString() : 'N/A';
                  break;
                case 'checkOutTime':
                  attendanceData.checkOutTime = record.checkOutTime ? new Date(record.checkOutTime).toLocaleTimeString() : 'N/A';
                  break;
                case 'totalHours':
                  attendanceData.totalHours = record.totalHours || 0;
                  break;
                case 'status':
                  attendanceData.status = record.status || 'Unknown';
                  break;
              }
            }
          });

          return attendanceData;
        });

        reportSummary = `This report contains attendance information for ${totalRecords} records ${startDate ? `from ${startDate.toLocaleDateString()}` : ''} ${endDate ? `to ${new Date(endDate).toLocaleDateString()}` : ''}.`;
        break;
      }

      default:
        throw new Error(`Unsupported report type: ${reportType}`);
    }

    // Format the report title
    const reportTitle = `${reportType.charAt(0).toUpperCase() + reportType.slice(1).replace('_', ' ')} Report`;

    // Create the report preview
    const reportPreview = {
      title: reportTitle,
      generatedAt: new Date().toISOString(),
      totalRecords,
      summary: reportSummary,
      sampleData: reportData.slice(0, 10) // Show first 10 records in preview
    };

    // Generate a unique filename for the report
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `${reportType}_report_${timestamp}.pdf`;
    const reportUrl = `/api/statistics/reports/download/${filename}`;

    // Store the report data in a session or database for later download
    // In a real implementation, you would generate a PDF and store it
    // For now, we'll just store the data in memory (not ideal for production)
    global.reportData = {
      [filename]: {
        title: reportTitle,
        generatedAt: new Date().toISOString(),
        data: reportData,
        summary: reportSummary
      }
    };

    // Create audit log entry for report generation
    await new AuditLog({
      userId: req.user.id,
      userInfo: {
        name: req.user.name,
        email: req.user.email,
        role: req.user.role
      },
      action: 'REPORT_CREATE',
      resourceType: 'REPORT',
      description: `Generated ${reportType} report with ${totalRecords} records`,
      ipAddress: req.ip || req.connection.remoteAddress || 'Unknown',
      userAgent: req.headers['user-agent'] || 'Unknown'
    }).save();

    res.status(200).json({
      success: true,
      message: 'Report generated successfully',
      preview: reportPreview,
      reportUrl
    });
  } catch (error) {
    console.error('Error generating report:', error);
    res.status(500).json({ message: 'Server error while generating report' });
  }
});

// Import jwt for token verification
const jwt = require('jsonwebtoken');

// Download report - support both GET and POST methods
const downloadReportHandler = async (req, res) => {
  try {
    const { filename } = req.params;

    // For POST requests, check for token in the body
    if (req.method === 'POST' && req.body.token) {
      try {
        // Verify the token manually
        const decoded = jwt.verify(req.body.token, process.env.JWT_SECRET || 'yourSecretKey');
        req.user = decoded;
      } catch (error) {
        console.error('Token verification failed:', error);
        return res.status(401).json({ success: false, message: 'Invalid token' });
      }
    }

    // Ensure user is authenticated
    if (!req.user) {
      return res.status(401).json({ success: false, message: 'Unauthorized: No token provided' });
    }

    // Check if the report exists
    if (!global.reportData || !global.reportData[filename]) {
      return res.status(404).json({ message: 'Report not found' });
    }

    const reportData = global.reportData[filename];

    // Create audit log entry for report download
    try {
      await new AuditLog({
        userId: req.user.id,
        userInfo: {
          name: req.user.name,
          email: req.user.email,
          role: req.user.role
        },
        action: 'REPORT_DOWNLOAD',
        resourceType: 'REPORT',
        description: `Downloaded report: ${filename}`,
        ipAddress: req.ip || req.connection.remoteAddress || 'Unknown',
        userAgent: req.headers['user-agent'] || 'Unknown'
      }).save();
    } catch (error) {
      console.error('Error creating audit log:', error);
      // Continue with download even if audit log fails
    }

    // Generate PDF report
    try {
      const PDFDocument = require('pdfkit');
      // Create a PDF with better margins and layout
      const doc = new PDFDocument({
        margin: 50,
        size: 'A4',
        bufferPages: true,
        autoFirstPage: true,
        layout: 'portrait'
      });

      // Set response headers for PDF download
      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Disposition', `attachment; filename=${filename}`);

      // Pipe the PDF to the response
      doc.pipe(res);

      // Define colors for consistent styling
      const colors = {
        primary: '#c62828',      // Dark red
        secondary: '#f44336',    // Red
        text: '#212121',         // Dark gray
        lightText: '#757575',    // Medium gray
        background: '#f5f5f5',   // Light gray
        headerBg: '#eeeeee',     // Slightly darker light gray
        border: '#e0e0e0',       // Border color
        altRow: '#f9f9f9'        // Alternate row color
      };

      // Add a styled header section with background
      doc.rect(0, 0, doc.page.width, 120)
         .fill(colors.primary);

      // Add company name/logo
      doc.fillColor('#ffffff')
         .fontSize(24)
         .font('Helvetica-Bold')
         .text('HR MANAGEMENT SYSTEM', 50, 40, { align: 'center' });

      // Add report title
      doc.fontSize(18)
         .font('Helvetica')
         .text(reportData.title || 'Report', 50, 70, { align: 'center' });

      // Add generation date
      doc.fontSize(10)
         .text(`Generated on: ${new Date(reportData.generatedAt || new Date()).toLocaleString()}`, 50, 95, { align: 'center' });

      // Reset text color for the rest of the document
      doc.fillColor(colors.text);

      // Add a section for summary
      doc.rect(50, 140, doc.page.width - 100, 80)
         .fillAndStroke(colors.background, colors.border);

      doc.fontSize(14)
         .font('Helvetica-Bold')
         .text('Summary', 70, 150);

      doc.fontSize(11)
         .font('Helvetica')
         .text(reportData.summary || 'Report summary', 70, 170, {
           width: doc.page.width - 140,
           align: 'left'
         });

      // Add total records with a styled box
      doc.rect(50, 240, doc.page.width - 100, 30)
         .fillAndStroke(colors.secondary, colors.border);

      doc.fillColor('#ffffff')
         .fontSize(12)
         .font('Helvetica-Bold')
         .text(`Total Records: ${reportData.data ? reportData.data.length : 0}`, 70, 250, {
           width: doc.page.width - 140,
           align: 'center'
         });

      // Reset fill color
      doc.fillColor(colors.text);

      // Start table at y position 290
      const tableTop = 290;

      // Create table header
      if (reportData.data && reportData.data.length > 0) {
        const firstItem = reportData.data[0];
        const keys = Object.keys(firstItem);

        // Limit the number of columns to display to prevent overcrowding
        const maxColumns = Math.min(keys.length, 6);
        const displayKeys = keys.slice(0, maxColumns);

        // Calculate table dimensions
        const tableWidth = doc.page.width - 100; // 50px margin on each side
        const tableLeft = 50;
        const rowHeight = 25;

        // Calculate column widths - allocate space proportionally based on content type
        const columnWidths = {};

        displayKeys.forEach(key => {
          // Determine column type and assign appropriate width
          if (key.toLowerCase().includes('name') || key.toLowerCase().includes('title')) {
            // Names and titles get more space
            columnWidths[key] = tableWidth * 0.25;
          } else if (key.toLowerCase().includes('date') || key.toLowerCase().includes('time')) {
            // Dates get medium space
            columnWidths[key] = tableWidth * 0.15;
          } else if (key.toLowerCase().includes('email')) {
            // Emails get more space
            columnWidths[key] = tableWidth * 0.25;
          } else if (key.toLowerCase().includes('status') || key.toLowerCase().includes('role') ||
                    key.toLowerCase().includes('department') || typeof firstItem[key] === 'boolean') {
            // Status fields get less space
            columnWidths[key] = tableWidth * 0.12;
          } else {
            // Default width for other fields
            columnWidths[key] = tableWidth * 0.15;
          }
        });

        // Normalize column widths to ensure they sum to tableWidth
        const totalWidth = Object.values(columnWidths).reduce((sum, width) => sum + width, 0);
        const scaleFactor = tableWidth / totalWidth;

        Object.keys(columnWidths).forEach(key => {
          columnWidths[key] = columnWidths[key] * scaleFactor;
        });

        // Function to draw a table row
        const drawTableRow = (data, isHeader, y, fillColor) => {
          // Draw row background
          doc.rect(tableLeft, y, tableWidth, rowHeight)
             .fillAndStroke(fillColor, colors.border);

          // Set text styling based on whether this is a header row
          if (isHeader) {
            doc.font('Helvetica-Bold').fontSize(10);
          } else {
            doc.font('Helvetica').fontSize(9);
          }

          // Draw cell content
          let xOffset = tableLeft;
          displayKeys.forEach(key => {
            const width = columnWidths[key];
            let text = isHeader
              ? key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase()) // Format header text
              : data[key];

            // Format cell values
            if (!isHeader) {
              // Format dates
              if (typeof text === 'string' && text.includes('T') && !isNaN(Date.parse(text))) {
                text = new Date(text).toLocaleDateString();
              }

              // Format boolean values
              if (typeof text === 'boolean') {
                text = text ? 'Yes' : 'No';
              }

              // Handle null/undefined
              if (text === null || text === undefined) {
                text = 'N/A';
              }

              // Convert to string if not already
              if (typeof text !== 'string') {
                text = text.toString();
              }

              // Truncate long text
              if (text.length > 30) {
                text = text.substring(0, 27) + '...';
              }
            }

            // Calculate vertical centering
            const textHeight = isHeader ? 10 : 9;
            const verticalCenter = y + (rowHeight - textHeight) / 2;

            // Draw the text
            doc.fillColor(isHeader ? '#ffffff' : colors.text)
               .text(text, xOffset + 5, verticalCenter, {
                 width: width - 10,
                 align: isHeader ? 'center' : (typeof data[key] === 'number' ? 'right' : 'left'),
                 lineBreak: false,
                 ellipsis: true
               });

            xOffset += width;
          });

          return y + rowHeight;
        };

        // Draw header row
        let yPos = tableTop;
        yPos = drawTableRow(firstItem, true, yPos, colors.secondary);

        // Draw data rows
        let rowColor = false;
        reportData.data.forEach((row, rowIndex) => {
          // Check if we need a new page
          if (yPos > doc.page.height - 100) {
            doc.addPage();

            // Add header to new page
            doc.rect(0, 0, doc.page.width, 50)
               .fill(colors.primary);

            doc.fillColor('#ffffff')
               .fontSize(14)
               .font('Helvetica-Bold')
               .text(reportData.title || 'Report', 50, 20, { align: 'center' });

            // Reset position for table header
            yPos = 70;

            // Redraw the header row on the new page
            yPos = drawTableRow(firstItem, true, yPos, colors.secondary);
          }

          // Draw the data row
          rowColor = !rowColor;
          yPos = drawTableRow(row, false, yPos, rowColor ? colors.altRow : '#ffffff');
        });

        // Draw table border
        doc.rect(tableLeft, tableTop, tableWidth, yPos - tableTop)
           .stroke(colors.border);
      } else {
        // No data available
        doc.rect(50, tableTop, doc.page.width - 100, 50)
           .fillAndStroke(colors.background, colors.border);

        doc.fontSize(12)
           .font('Helvetica-Bold')
           .fillColor(colors.text)
           .text('No data available for this report.', 50, tableTop + 15, {
             align: 'center',
             width: doc.page.width - 100
           });
      }

      // Add footer to each page
      const pageCount = doc.bufferedPageRange().count;
      for (let i = 0; i < pageCount; i++) {
        doc.switchToPage(i);

        // Add footer background
        doc.rect(0, doc.page.height - 40, doc.page.width, 40)
           .fill(colors.primary);

        // Add page number
        doc.fillColor('#ffffff')
           .fontSize(8)
           .font('Helvetica')
           .text(`Page ${i + 1} of ${pageCount}`,
             50, doc.page.height - 25,
             { align: 'left', width: 100 });

        // Add company footer
        doc.fontSize(8)
           .font('Helvetica-Bold')
           .text('HR Management System - Confidential',
             0, doc.page.height - 25,
             { align: 'center', width: doc.page.width });

        // Add date in footer
        const today = new Date().toLocaleDateString();
        doc.fontSize(8)
           .font('Helvetica')
           .text(today,
             doc.page.width - 150, doc.page.height - 25,
             { align: 'right', width: 100 });
      }

      // Finalize the PDF
      doc.end();

      console.log(`Generated PDF report: ${filename}`);
    } catch (pdfError) {
      console.error('Error generating PDF:', pdfError);
      return res.status(500).json({ message: 'Error generating PDF report' });
    }
  } catch (error) {
    console.error('Error in downloadReportHandler:', error);
    res.status(500).json({ message: 'Server error while downloading report' });
  }
};

// Support GET method (with Authorization header)
router.get('/reports/download/:filename', authenticate, authorizeRoles('admin'), downloadReportHandler);

// Support POST method (with token in body)
router.post('/reports/download/:filename', async (req, res) => {
  await downloadReportHandler(req, res);
});

module.exports = router;
