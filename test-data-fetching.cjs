const axios = require('axios');

async function testDataFetching() {
  try {
    console.log('🔍 TESTING DATA FETCHING FUNCTIONALITY\n');

    // Step 1: Create test user
    console.log('1. Creating test user...');
    try {
      const createUserResponse = await axios.get('http://localhost:5000/create-test-user');
      console.log('✅ Test user created/updated:', createUserResponse.data.message);
    } catch (error) {
      console.log('⚠️ Test user creation failed (might already exist):', error.response?.data?.message || error.message);
    }

    // Step 2: Login
    console.log('\n2. Testing login...');
    const loginResponse = await axios.post('http://localhost:5000/api/auth/login', {
      email: '<EMAIL>',
      password: 'test123'
    });

    if (!loginResponse.data.success) {
      throw new Error('Login failed: ' + loginResponse.data.message);
    }

    const token = loginResponse.data.token;
    const userInfo = {
      name: loginResponse.data.name,
      email: loginResponse.data.email,
      role: loginResponse.data.role,
      userId: loginResponse.data.userId
    };

    console.log('✅ Login successful!');
    console.log('   User:', userInfo.name);
    console.log('   Role:', userInfo.role);
    console.log('   Token received:', token ? 'Yes' : 'No');

    // Headers for authenticated requests
    const authHeaders = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };

    // Step 3: Test various data fetching endpoints
    console.log('\n3. Testing data fetching endpoints...\n');

    const endpoints = [
      {
        name: 'User Profile',
        url: '/api/user/profile',
        method: 'GET'
      },
      {
        name: 'User Tasks',
        url: '/api/tasks/user',
        method: 'GET'
      },
      {
        name: 'User Leave Requests',
        url: '/api/user/leave-requests',
        method: 'GET'
      },
      {
        name: 'User Attendance',
        url: '/api/user/attendance',
        method: 'GET'
      },
      {
        name: 'Available Jobs',
        url: '/api/jobs',
        method: 'GET'
      },
      {
        name: 'User Applications',
        url: '/api/applications/user',
        method: 'GET'
      },
      {
        name: 'User Notifications',
        url: '/api/notifications',
        method: 'GET'
      }
    ];

    let successCount = 0;
    let totalCount = endpoints.length;

    for (const endpoint of endpoints) {
      try {
        console.log(`   Testing ${endpoint.name}...`);
        
        const response = await axios({
          method: endpoint.method,
          url: `http://localhost:5000${endpoint.url}`,
          headers: authHeaders,
          timeout: 5000
        });

        console.log(`   ✅ ${endpoint.name}: SUCCESS (${response.status})`);
        
        // Log data structure if available
        if (response.data) {
          if (Array.isArray(response.data)) {
            console.log(`      Data: Array with ${response.data.length} items`);
          } else if (typeof response.data === 'object') {
            const keys = Object.keys(response.data);
            console.log(`      Data: Object with keys: ${keys.slice(0, 3).join(', ')}${keys.length > 3 ? '...' : ''}`);
          }
        }
        
        successCount++;
      } catch (error) {
        const status = error.response?.status || 'No response';
        const message = error.response?.data?.message || error.message;
        console.log(`   ❌ ${endpoint.name}: FAILED (${status}) - ${message}`);
      }
    }

    // Step 4: Summary
    console.log(`\n4. SUMMARY:`);
    console.log(`   ✅ Successful endpoints: ${successCount}/${totalCount}`);
    console.log(`   ❌ Failed endpoints: ${totalCount - successCount}/${totalCount}`);
    
    if (successCount > 0) {
      console.log('\n🎉 DATA FETCHING IS WORKING! The system can retrieve data successfully.');
      console.log('   The frontend should be able to fetch and display data properly.');
    } else {
      console.log('\n⚠️ NO ENDPOINTS WORKING! There might be authentication or server issues.');
    }

    // Step 5: Test a simple data creation to verify write operations
    console.log('\n5. Testing data creation (write operations)...');
    
    try {
      // Test creating a leave request
      const leaveRequestData = {
        leaveType: 'Vacation',
        startDate: new Date().toISOString().split('T')[0],
        endDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        reason: 'Test leave request for data fetching verification'
      };

      const createResponse = await axios.post(
        'http://localhost:5000/api/user/leave-requests',
        leaveRequestData,
        { headers: authHeaders, timeout: 5000 }
      );

      console.log('   ✅ Data creation: SUCCESS - Leave request created');
      console.log('      Created ID:', createResponse.data.leaveRequest?._id || 'Unknown');
      
    } catch (error) {
      const status = error.response?.status || 'No response';
      const message = error.response?.data?.message || error.message;
      console.log(`   ❌ Data creation: FAILED (${status}) - ${message}`);
    }

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    if (error.response) {
      console.error('   Status:', error.response.status);
      console.error('   Data:', error.response.data);
    }
  }
}

// Run the test
testDataFetching();
