/**
 * Smart Assistant Provider
 * React Context Provider for Smart AI Assistant functionality
 */

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import smartAssistantService from '../../Services/SmartAssistantService';
import { toast } from 'react-toastify';

// Create Smart Assistant Context
const SmartAssistantContext = createContext();

// Custom hook to use Smart Assistant Context
export const useSmartAssistant = () => {
  const context = useContext(SmartAssistantContext);
  if (!context) {
    throw new Error('useSmartAssistant must be used within a SmartAssistantProvider');
  }
  return context;
};

// Smart Assistant Provider Component
const SmartAssistantProvider = ({ children }) => {
  // State management
  const [insights, setInsights] = useState([]);
  const [preferences, setPreferences] = useState({
    frequency: 'medium',
    categories: ['all'],
    tone: 'professional',
    enabled: true
  });
  const [analytics, setAnalytics] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // Load initial data
  useEffect(() => {
    loadInitialData();
  }, []);

  /**
   * Load initial smart assistant data
   */
  const loadInitialData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Load user preferences and insights in parallel
      const [preferencesResult, insightsResult] = await Promise.allSettled([
        smartAssistantService.getPreferences(),
        smartAssistantService.getInsights({ limit: 10 })
      ]);

      // Handle preferences
      if (preferencesResult.status === 'fulfilled' && preferencesResult.value?.data) {
        setPreferences(prev => ({ ...prev, ...preferencesResult.value.data }));
      }

      // Handle insights
      if (insightsResult.status === 'fulfilled' && insightsResult.value?.data?.insights) {
        setInsights(insightsResult.value.data.insights);
      }

    } catch (error) {
      console.error('Error loading smart assistant data:', error);
      setError('Failed to load smart assistant data');
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Refresh insights from the backend
   */
  const refreshInsights = useCallback(async (filters = {}) => {
    try {
      setIsLoading(true);
      const response = await smartAssistantService.getInsights(filters);
      
      if (response?.data?.insights) {
        setInsights(response.data.insights);
      }
      
      return response;
    } catch (error) {
      console.error('Error refreshing insights:', error);
      setError('Failed to refresh insights');
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Trigger behavioral analysis
   */
  const triggerAnalysis = useCallback(async (timeWindow = null) => {
    try {
      setIsLoading(true);
      const response = await smartAssistantService.analyzeAndSuggest(timeWindow);
      
      // Refresh insights after analysis
      await refreshInsights();
      
      return response;
    } catch (error) {
      console.error('Error triggering analysis:', error);
      setError('Failed to trigger analysis');
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [refreshInsights]);

  /**
   * Provide feedback on an insight
   */
  const provideFeedback = useCallback(async (insightId, rating, comment = '') => {
    try {
      const response = await smartAssistantService.provideFeedback(insightId, rating, comment);
      
      // Update local insights state
      setInsights(prev => prev.map(insight => 
        insight.id === insightId 
          ? { ...insight, feedback: { rating, comment, timestamp: new Date() } }
          : insight
      ));

      toast.success('Feedback submitted successfully');
      return response;
    } catch (error) {
      console.error('Error providing feedback:', error);
      toast.error('Failed to submit feedback');
      throw error;
    }
  }, []);

  /**
   * Dismiss an insight
   */
  const dismissInsight = useCallback(async (insightId) => {
    try {
      await smartAssistantService.dismissInsight(insightId);
      
      // Remove from local state
      setInsights(prev => prev.filter(insight => insight.id !== insightId));
      
      toast.success('Insight dismissed');
    } catch (error) {
      console.error('Error dismissing insight:', error);
      toast.error('Failed to dismiss insight');
      throw error;
    }
  }, []);

  /**
   * Update user preferences
   */
  const updatePreferences = useCallback(async (newPreferences) => {
    try {
      const response = await smartAssistantService.updatePreferences(newPreferences);
      
      if (response?.data) {
        setPreferences(prev => ({ ...prev, ...response.data }));
        toast.success('Preferences updated successfully');
      }
      
      return response;
    } catch (error) {
      console.error('Error updating preferences:', error);
      toast.error('Failed to update preferences');
      throw error;
    }
  }, []);

  /**
   * Load analytics data
   */
  const loadAnalytics = useCallback(async () => {
    try {
      const response = await smartAssistantService.getAnalytics();
      
      if (response?.data) {
        setAnalytics(response.data);
      }
      
      return response;
    } catch (error) {
      console.error('Error loading analytics:', error);
      setError('Failed to load analytics');
      throw error;
    }
  }, []);

  /**
   * Track user operation for behavioral analysis
   */
  const trackOperation = useCallback(async (operation, resourceType, resourceId = null, metadata = {}) => {
    try {
      // Track operation in background - don't block UI
      smartAssistantService.trackOperation(operation, resourceType, resourceId, metadata);
    } catch (error) {
      console.error('Error tracking operation:', error);
      // Don't throw error for tracking - it's not critical
    }
  }, []);

  /**
   * Get filtered insights
   */
  const getFilteredInsights = useCallback((filters = {}) => {
    let filtered = [...insights];

    if (filters.category && filters.category !== 'all') {
      filtered = filtered.filter(insight => insight.category === filters.category);
    }

    if (filters.priority) {
      filtered = filtered.filter(insight => insight.priority === filters.priority);
    }

    if (filters.status) {
      filtered = filtered.filter(insight => insight.status === filters.status);
    }

    return filtered;
  }, [insights]);

  /**
   * Clear error state
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Context value
  const contextValue = {
    // State
    insights,
    preferences,
    analytics,
    isLoading,
    error,

    // Actions
    refreshInsights,
    triggerAnalysis,
    provideFeedback,
    dismissInsight,
    updatePreferences,
    loadAnalytics,
    trackOperation,
    getFilteredInsights,
    clearError,

    // Utility functions
    getPriorityColor: smartAssistantService.getPriorityColor,
    getCategoryIcon: smartAssistantService.getCategoryIcon,
    formatSuggestionMessage: smartAssistantService.formatSuggestionMessage,
    shouldShowSuggestion: smartAssistantService.shouldShowSuggestion,

    // Service instance for advanced usage
    service: smartAssistantService
  };

  return (
    <SmartAssistantContext.Provider value={contextValue}>
      {children}
    </SmartAssistantContext.Provider>
  );
};

export default SmartAssistantProvider;
