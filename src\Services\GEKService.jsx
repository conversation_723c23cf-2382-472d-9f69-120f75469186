import api from './ApiService';

/**
 * GEK Service - Provides methods to interact with the General Estimating Knowledge API
 */
class GEKService {
  /**
   * Get fit score for a user and task category
   * @param {string} userId - User ID
   * @param {string} category - Task category
   * @param {string} priority - Task priority (optional)
   * @param {boolean} recalculate - Whether to force recalculation (optional)
   * @returns {Promise<Object>} - Fit score data
   */
  async getFitScore(userId, category, priority = 'Medium', recalculate = false) {
    try {
      const response = await api.get(`/gek/fit-score/${userId}`, {
        params: { category, priority, recalculate }
      });
      return response.data;
    } catch (error) {
      console.error('Error getting fit score:', error);
      throw error;
    }
  }

  /**
   * Get all fit score estimates for a user
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - User estimates data
   */
  async getUserEstimates(userId) {
    try {
      const response = await api.get(`/gek/user-estimates/${userId}`);
      return response.data;
    } catch (error) {
      console.error('Error getting user estimates:', error);
      throw error;
    }
  }

  /**
   * Get ranked employees for a task
   * @param {Object} taskDetails - Task details including category and priority
   * @param {Array} userIds - List of user IDs to rank (optional)
   * @param {number} limit - Maximum number of results to return (optional)
   * @returns {Promise<Object>} - Ranked employees data
   */
  async getRankedEmployees(taskDetails, userIds = null, limit = 5) {
    try {
      const response = await api.post('/gek/ranked-employees', {
        taskDetails,
        userIds,
        limit
      });
      return response.data;
    } catch (error) {
      console.error('Error getting ranked employees:', error);
      throw error;
    }
  }

  /**
   * Get ranked employees within a department for a task
   * @param {string} department - Department name
   * @param {string} category - Task category
   * @param {string} priority - Task priority (optional)
   * @param {number} limit - Maximum number of results to return (optional)
   * @returns {Promise<Object>} - Department ranking data
   */
  async getDepartmentRanking(department, category, priority = 'Medium', limit = 10) {
    try {
      const response = await api.get('/gek/department-ranking', {
        params: { department, category, priority, limit }
      });
      return response.data;
    } catch (error) {
      console.error('Error getting department ranking:', error);
      throw error;
    }
  }

  /**
   * Get task performance metrics for a user
   * @param {string} userId - User ID
   * @param {string} category - Task category (optional)
   * @param {number} days - Number of days to look back (optional)
   * @returns {Promise<Object>} - Task metrics data
   */
  async getTaskMetrics(userId, category = null, days = 90) {
    try {
      const response = await api.get(`/gek/task-metrics/${userId}`, {
        params: { category, days }
      });
      return response.data;
    } catch (error) {
      console.error('Error getting task metrics:', error);
      throw error;
    }
  }

  /**
   * Get attendance metrics for a user
   * @param {string} userId - User ID
   * @param {number} days - Number of days to look back (optional)
   * @returns {Promise<Object>} - Attendance metrics data
   */
  async getAttendanceMetrics(userId, days = 90) {
    try {
      const response = await api.get(`/gek/attendance-metrics/${userId}`, {
        params: { days }
      });
      return response.data;
    } catch (error) {
      console.error('Error getting attendance metrics:', error);
      throw error;
    }
  }

  /**
   * Get evaluation metrics for a user
   * @param {string} userId - User ID
   * @param {number} days - Number of days to look back (optional)
   * @returns {Promise<Object>} - Evaluation metrics data
   */
  async getEvaluationMetrics(userId, days = 90) {
    try {
      const response = await api.get(`/gek/evaluation-metrics/${userId}`, {
        params: { days }
      });
      return response.data;
    } catch (error) {
      console.error('Error getting evaluation metrics:', error);
      throw error;
    }
  }

  // ==================== TASK INTEGRATION METHODS ====================

  /**
   * Get available tasks for GEK assignment
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} - Available tasks data
   */
  async getAvailableTasks(params = {}) {
    try {
      const response = await api.get('/gek/available-tasks', { params });
      return response.data;
    } catch (error) {
      console.error('Error getting available tasks:', error);
      throw error;
    }
  }

  /**
   * Get best employee matches for a specific task
   * @param {string} taskId - Task ID
   * @param {number} limit - Number of matches to return (optional)
   * @returns {Promise<Object>} - Employee matches data
   */
  async getTaskEmployeeMatches(taskId, limit = 10) {
    try {
      const response = await api.get(`/gek/task-employee-match/${taskId}`, {
        params: { limit }
      });
      return response.data;
    } catch (error) {
      console.error('Error getting task-employee matches:', error);
      throw error;
    }
  }

  /**
   * Reassign a task to a different employee
   * @param {string} taskId - Task ID
   * @param {string} newAssigneeId - New assignee user ID
   * @param {string} reason - Reason for reassignment (optional)
   * @returns {Promise<Object>} - Reassignment result
   */
  async reassignTask(taskId, newAssigneeId, reason = '') {
    try {
      const response = await api.put(`/gek/reassign-task/${taskId}`, {
        newAssigneeId,
        reason
      });
      return response.data;
    } catch (error) {
      console.error('Error reassigning task:', error);
      throw error;
    }
  }

  // ==================== ANALYTICS METHODS ====================

  /**
   * Get GEK system overview analytics
   * @param {number} days - Number of days to look back (optional)
   * @returns {Promise<Object>} - Overview analytics data
   */
  async getAnalyticsOverview(days = 90) {
    try {
      const response = await api.get('/gek/analytics/overview', {
        params: { days }
      });
      return response.data;
    } catch (error) {
      console.error('Error getting analytics overview:', error);
      throw error;
    }
  }

  /**
   * Get department-wise GEK performance analytics
   * @param {number} days - Number of days to look back (optional)
   * @returns {Promise<Object>} - Department performance data
   */
  async getDepartmentPerformance(days = 90) {
    try {
      const response = await api.get('/gek/analytics/department-performance', {
        params: { days }
      });
      return response.data;
    } catch (error) {
      console.error('Error getting department performance:', error);
      throw error;
    }
  }

  /**
   * Get GEK performance trends over time
   * @param {number} days - Number of days to look back (optional)
   * @param {string} interval - Time interval ('day', 'week', 'month')
   * @returns {Promise<Object>} - Trends data
   */
  async getPerformanceTrends(days = 90, interval = 'week') {
    try {
      const response = await api.get('/gek/analytics/trends', {
        params: { days, interval }
      });
      return response.data;
    } catch (error) {
      console.error('Error getting performance trends:', error);
      throw error;
    }
  }
}

export default new GEKService();
