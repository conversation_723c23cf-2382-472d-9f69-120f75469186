import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Button,
  Chip,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  CircularProgress,
  Alert,
  Divider,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Tooltip,
  LinearProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import {
  Assignment,
  Person,
  TrendingUp,
  Speed,
  Schedule,
  Search,
  FilterList,
  Refresh,
  CheckCircle,
  SwapHoriz,
  Visibility,
  Close,
  ExpandMore,
  Psychology,
  AutoAwesome,
  Analytics,
  SmartToy
} from '@mui/icons-material';
import { format } from 'date-fns';
import { showSuccessToast, showErrorToast, TOAST_CATEGORIES } from '../../Utils/toastUtils';
import GEKService from '../../Services/GEKService';
import TaskService from '../../Services/TaskService';

/**
 * Enhanced GEK Task Assignment Component
 * AI/NLP-powered intelligent task assignment with advanced employee matching
 */
const GEKTaskAssignment = () => {
  // State management
  const [unassignedTasks, setUnassignedTasks] = useState([]);
  const [selectedTask, setSelectedTask] = useState(null);
  const [employeeMatches, setEmployeeMatches] = useState([]);
  const [loading, setLoading] = useState(false);
  const [matchesLoading, setMatchesLoading] = useState(false);
  const [error, setError] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [lastRefresh, setLastRefresh] = useState(new Date());
  const [filters, setFilters] = useState({
    category: '',
    priority: '',
    deadline: ''
  });

  // Dialog states
  const [assignDialogOpen, setAssignDialogOpen] = useState(false);
  const [selectedEmployee, setSelectedEmployee] = useState(null);
  const [assignmentReason, setAssignmentReason] = useState('');

  // AI Analysis state
  const [aiAnalysis, setAiAnalysis] = useState(null);
  const [analysisLoading, setAnalysisLoading] = useState(false);

  // Constants
  const taskCategories = [
    'General', 'Development', 'Design', 'Marketing', 'HR',
    'Finance', 'Operations', 'Project', 'Administrative',
    'Training', 'Evaluation', 'Other'
  ];
  const taskPriorities = ['Low', 'Medium', 'High', 'Urgent'];

  // Load unassigned tasks on component mount and set up auto-refresh
  useEffect(() => {
    loadUnassignedTasks();

    // Set up auto-refresh every 30 seconds for real-time data
    const refreshInterval = setInterval(() => {
      if (!loading && !matchesLoading) {
        loadUnassignedTasks();
      }
    }, 30000);

    return () => clearInterval(refreshInterval);
  }, []);

  // Reload tasks when filters change
  useEffect(() => {
    if (searchQuery || filters.category || filters.priority) {
      const debounceTimer = setTimeout(() => {
        loadUnassignedTasks();
      }, 500);

      return () => clearTimeout(debounceTimer);
    }
  }, [searchQuery, filters]);

  // Load unassigned tasks with real-time data
  const loadUnassignedTasks = async () => {
    setLoading(true);
    setError(null);

    try {
      // Get real-time unassigned tasks
      const response = await TaskService.getHRTasks({
        status: 'Unassigned',
        search: searchQuery,
        ...filters,
        limit: 50, // Get more tasks for better selection
        sortBy: 'createdAt',
        sortOrder: 'desc'
      });

      const tasks = response.tasks || [];
      setUnassignedTasks(tasks);
      setLastRefresh(new Date());
    } catch (err) {
      console.error('Error loading unassigned tasks:', err);
      setError('Failed to load unassigned tasks');
      showErrorToast('Failed to load unassigned tasks', TOAST_CATEGORIES.TASK, 'loadFailed');
    } finally {
      setLoading(false);
    }
  };

  // Handle task selection and get AI-powered employee matches
  const handleTaskSelect = async (task) => {
    setSelectedTask(task);
    setMatchesLoading(true);
    setError(null);
    setAiAnalysis(null);

    try {
      // Get GEK-based employee recommendations
      const response = await GEKService.getTaskEmployeeMatches(task._id);
      setEmployeeMatches(response.rankedEmployees || []);

      // Get AI analysis of the task
      await performAIAnalysis(task);
    } catch (err) {
      console.error('Error getting employee matches:', err);
      setError('Failed to get employee recommendations');
      showErrorToast('Failed to get employee recommendations', TOAST_CATEGORIES.TASK, 'matchesFailed');
    } finally {
      setMatchesLoading(false);
    }
  };

  // Perform AI/NLP analysis of the task
  const performAIAnalysis = async (task) => {
    setAnalysisLoading(true);

    try {
      // Simulate AI/NLP analysis (in real implementation, this would call an AI service)
      const analysis = await analyzeTaskWithAI(task);
      setAiAnalysis(analysis);
    } catch (err) {
      console.error('Error performing AI analysis:', err);
      // Don't show error for AI analysis failure - it's supplementary
    } finally {
      setAnalysisLoading(false);
    }
  };

  // Simulate AI/NLP task analysis
  const analyzeTaskWithAI = async (task) => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1500));

    // Extract keywords and analyze task complexity
    const keywords = extractKeywords(task.title + ' ' + task.description);
    const complexity = analyzeComplexity(task.description, task.priority);
    const skillsRequired = identifyRequiredSkills(task.description, task.category);
    const timeEstimate = estimateTimeRequirement(task.description, task.priority, task.category);

    return {
      keywords,
      complexity,
      skillsRequired,
      timeEstimate,
      recommendations: generateAIRecommendations(task, complexity, skillsRequired)
    };
  };

  // Extract keywords using simple NLP
  const extractKeywords = (text) => {
    const commonWords = ['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those'];
    
    return text.toLowerCase()
      .replace(/[^\w\s]/g, '')
      .split(/\s+/)
      .filter(word => word.length > 3 && !commonWords.includes(word))
      .reduce((acc, word) => {
        acc[word] = (acc[word] || 0) + 1;
        return acc;
      }, {});
  };

  // Analyze task complexity
  const analyzeComplexity = (description, priority) => {
    const complexityIndicators = ['complex', 'advanced', 'sophisticated', 'intricate', 'detailed', 'comprehensive'];
    const simpleIndicators = ['simple', 'basic', 'easy', 'straightforward', 'quick'];
    
    const text = description.toLowerCase();
    const complexCount = complexityIndicators.filter(word => text.includes(word)).length;
    const simpleCount = simpleIndicators.filter(word => text.includes(word)).length;
    
    let baseComplexity = priority === 'Urgent' ? 4 : priority === 'High' ? 3 : priority === 'Medium' ? 2 : 1;
    
    if (complexCount > simpleCount) baseComplexity += 1;
    if (simpleCount > complexCount) baseComplexity -= 1;
    
    return Math.max(1, Math.min(5, baseComplexity));
  };

  // Identify required skills
  const identifyRequiredSkills = (description, category) => {
    const skillMap = {
      'Development': ['programming', 'coding', 'javascript', 'python', 'react', 'node.js', 'database', 'api'],
      'Design': ['design', 'ui', 'ux', 'photoshop', 'figma', 'creative', 'visual', 'branding'],
      'Marketing': ['marketing', 'social media', 'content', 'seo', 'analytics', 'campaigns', 'advertising'],
      'HR': ['recruitment', 'interviewing', 'employee relations', 'policies', 'training', 'performance'],
      'Finance': ['accounting', 'budgeting', 'financial analysis', 'excel', 'reporting', 'compliance'],
      'Operations': ['process improvement', 'logistics', 'coordination', 'planning', 'efficiency']
    };

    const categorySkills = skillMap[category] || [];
    const text = description.toLowerCase();
    
    return categorySkills.filter(skill => text.includes(skill.toLowerCase()));
  };

  // Estimate time requirement
  const estimateTimeRequirement = (description, priority, category) => {
    const baseHours = {
      'Development': 16,
      'Design': 12,
      'Marketing': 8,
      'HR': 6,
      'Finance': 10,
      'Operations': 8,
      'General': 6
    };

    const priorityMultiplier = {
      'Urgent': 0.7, // Urgent tasks often need quick solutions
      'High': 1.0,
      'Medium': 1.2,
      'Low': 1.5
    };

    const base = baseHours[category] || baseHours['General'];
    const multiplier = priorityMultiplier[priority] || 1.0;
    const wordCount = description.split(' ').length;
    const complexityFactor = wordCount > 100 ? 1.3 : wordCount > 50 ? 1.1 : 1.0;

    return Math.round(base * multiplier * complexityFactor);
  };

  // Generate AI recommendations
  const generateAIRecommendations = (task, complexity, skillsRequired) => {
    const recommendations = [];

    if (complexity >= 4) {
      recommendations.push('Consider assigning to senior team members due to high complexity');
    }

    if (skillsRequired.length > 3) {
      recommendations.push('Task requires multiple skills - consider team collaboration');
    }

    if (task.priority === 'Urgent') {
      recommendations.push('Prioritize employees with fastest completion rates');
    }

    if (skillsRequired.length === 0) {
      recommendations.push('General task - suitable for cross-training opportunities');
    }

    return recommendations;
  };

  // Handle task assignment
  const handleAssignTask = (employee) => {
    setSelectedEmployee(employee);
    setAssignDialogOpen(true);
  };

  // Confirm task assignment
  const confirmAssignment = async () => {
    if (!selectedEmployee || !selectedTask) return;

    setLoading(true);
    try {
      // Use TaskService to assign the task
      const response = await TaskService.assignTask(
        selectedTask._id,
        selectedEmployee.user._id,
        assignmentReason || `AI-recommended assignment based on GEK analysis. Fit Score: ${Math.round(selectedEmployee.fitScore)}%`
      );

      showSuccessToast(
        `Task "${selectedTask.title}" successfully assigned to ${selectedEmployee.user.name}`,
        TOAST_CATEGORIES.TASK,
        'taskAssigned'
      );

      // Refresh tasks and clear selection
      await loadUnassignedTasks();
      setSelectedTask(null);
      setEmployeeMatches([]);
      setAiAnalysis(null);
      setAssignDialogOpen(false);
      setSelectedEmployee(null);
      setAssignmentReason('');
    } catch (err) {
      console.error('Error assigning task:', err);
      const errorMessage = err.response?.data?.message || 'Failed to assign task';
      showErrorToast(errorMessage, TOAST_CATEGORIES.TASK, 'assignFailed');
    } finally {
      setLoading(false);
    }
  };

  // Apply filters
  const applyFilters = () => {
    loadUnassignedTasks();
  };

  // Clear filters
  const clearFilters = () => {
    setSearchQuery('');
    setFilters({
      category: '',
      priority: '',
      deadline: ''
    });
    setTimeout(loadUnassignedTasks, 100);
  };

  // Get priority color
  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'Urgent': return 'error';
      case 'High': return 'warning';
      case 'Medium': return 'info';
      case 'Low': return 'success';
      default: return 'default';
    }
  };

  // Get fit score color
  const getFitScoreColor = (score) => {
    if (score >= 80) return 'success';
    if (score >= 60) return 'warning';
    return 'error';
  };

  return (
    <Box>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
          <Typography variant="h4" component="h1" fontWeight={700}>
            <SmartToy sx={{ mr: 2, verticalAlign: 'middle', color: 'primary.main' }} />
            AI-Powered Task Assignment
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Chip
              icon={<Schedule />}
              label={`Last updated: ${lastRefresh.toLocaleTimeString()}`}
              size="small"
              variant="outlined"
              color="primary"
            />
            <Chip
              icon={<Assignment />}
              label={`${unassignedTasks.length} unassigned tasks`}
              size="small"
              color="warning"
            />
          </Box>
        </Box>
        <Typography variant="body1" color="text.secondary">
          Intelligent task assignment using AI/NLP analysis and GEK performance data to find the perfect employee-task matches.
          Data refreshes automatically every 30 seconds.
        </Typography>
      </Box>

      {/* Error Alert */}
      {error && (
        <Alert
          severity="error"
          sx={{ mb: 3 }}
          action={
            <Button color="inherit" size="small" onClick={loadUnassignedTasks}>
              Retry
            </Button>
          }
        >
          {error}
        </Alert>
      )}

      {/* Filters and Search */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                placeholder="Search tasks..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Search />
                    </InputAdornment>
                  )
                }}
              />
            </Grid>
            <Grid item xs={12} md={2}>
              <FormControl fullWidth>
                <InputLabel>Category</InputLabel>
                <Select
                  value={filters.category}
                  label="Category"
                  onChange={(e) => setFilters({ ...filters, category: e.target.value })}
                >
                  <MenuItem value="">All</MenuItem>
                  {taskCategories.map((category) => (
                    <MenuItem key={category} value={category}>{category}</MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <FormControl fullWidth>
                <InputLabel>Priority</InputLabel>
                <Select
                  value={filters.priority}
                  label="Priority"
                  onChange={(e) => setFilters({ ...filters, priority: e.target.value })}
                >
                  <MenuItem value="">All</MenuItem>
                  {taskPriorities.map((priority) => (
                    <MenuItem key={priority} value={priority}>{priority}</MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={3}>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Button
                  variant="contained"
                  onClick={applyFilters}
                  startIcon={<FilterList />}
                  disabled={loading}
                >
                  Apply
                </Button>
                <Button
                  variant="outlined"
                  onClick={clearFilters}
                  disabled={loading}
                >
                  Clear
                </Button>
                <IconButton onClick={loadUnassignedTasks} disabled={loading}>
                  <Refresh />
                </IconButton>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Main Content */}
      <Grid container spacing={3}>
        {/* Unassigned Tasks List */}
        <Grid item xs={12} lg={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                <Assignment sx={{ mr: 1, verticalAlign: 'middle' }} />
                Unassigned Tasks ({unassignedTasks.length})
              </Typography>

              {loading ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
                  <CircularProgress />
                </Box>
              ) : unassignedTasks.length === 0 ? (
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <Typography color="text.secondary">
                    No unassigned tasks found
                  </Typography>
                  <Button
                    variant="outlined"
                    sx={{ mt: 2 }}
                    onClick={loadUnassignedTasks}
                    startIcon={<Refresh />}
                  >
                    Refresh
                  </Button>
                </Box>
              ) : (
                <List>
                  {unassignedTasks.map((task, index) => (
                    <ListItem
                      key={task._id}
                      button
                      onClick={() => handleTaskSelect(task)}
                      selected={selectedTask?._id === task._id}
                      sx={{
                        border: '1px solid',
                        borderColor: selectedTask?._id === task._id ? 'primary.main' : 'divider',
                        borderRadius: 1,
                        mb: 1,
                        bgcolor: selectedTask?._id === task._id ? 'action.selected' : 'background.paper'
                      }}
                    >
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Typography variant="subtitle1" fontWeight="medium">
                              {task.title}
                            </Typography>
                            <Chip
                              label={task.priority}
                              size="small"
                              color={getPriorityColor(task.priority)}
                            />
                          </Box>
                        }
                        secondary={
                          <Box>
                            <Typography variant="body2" color="text.secondary" noWrap>
                              {task.description?.substring(0, 80)}
                              {task.description?.length > 80 ? '...' : ''}
                            </Typography>
                            <Box sx={{ display: 'flex', gap: 1, mt: 0.5 }}>
                              <Chip label={task.category} size="small" variant="outlined" />
                              <Chip
                                label={`Due: ${format(new Date(task.deadline), 'MMM dd')}`}
                                size="small"
                                variant="outlined"
                              />
                            </Box>
                          </Box>
                        }
                      />
                      {selectedTask?._id === task._id && (
                        <AutoAwesome color="primary" />
                      )}
                    </ListItem>
                  ))}
                </List>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* AI Analysis and Employee Matches */}
        <Grid item xs={12} lg={6}>
          {selectedTask ? (
            <Box>
              {/* AI Analysis Section */}
              <Card sx={{ mb: 2 }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    <Psychology sx={{ mr: 1, verticalAlign: 'middle', color: 'secondary.main' }} />
                    AI Task Analysis
                  </Typography>

                  {analysisLoading ? (
                    <Box sx={{ display: 'flex', alignItems: 'center', py: 2 }}>
                      <CircularProgress size={20} sx={{ mr: 2 }} />
                      <Typography variant="body2">Analyzing task with AI/NLP...</Typography>
                    </Box>
                  ) : aiAnalysis ? (
                    <Box>
                      <Grid container spacing={2}>
                        <Grid item xs={6}>
                          <Typography variant="body2" color="text.secondary">
                            Complexity Level
                          </Typography>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <LinearProgress
                              variant="determinate"
                              value={aiAnalysis.complexity * 20}
                              sx={{ flexGrow: 1, height: 8, borderRadius: 4 }}
                            />
                            <Typography variant="body2" fontWeight="medium">
                              {aiAnalysis.complexity}/5
                            </Typography>
                          </Box>
                        </Grid>
                        <Grid item xs={6}>
                          <Typography variant="body2" color="text.secondary">
                            Estimated Time
                          </Typography>
                          <Typography variant="h6" color="primary">
                            {aiAnalysis.timeEstimate}h
                          </Typography>
                        </Grid>
                      </Grid>

                      {aiAnalysis.skillsRequired.length > 0 && (
                        <Box sx={{ mt: 2 }}>
                          <Typography variant="body2" color="text.secondary" gutterBottom>
                            Required Skills
                          </Typography>
                          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                            {aiAnalysis.skillsRequired.map((skill, index) => (
                              <Chip
                                key={index}
                                label={skill}
                                size="small"
                                color="secondary"
                                variant="outlined"
                              />
                            ))}
                          </Box>
                        </Box>
                      )}

                      {aiAnalysis.recommendations.length > 0 && (
                        <Accordion sx={{ mt: 2 }}>
                          <AccordionSummary expandIcon={<ExpandMore />}>
                            <Typography variant="body2" fontWeight="medium">
                              AI Recommendations ({aiAnalysis.recommendations.length})
                            </Typography>
                          </AccordionSummary>
                          <AccordionDetails>
                            <List dense>
                              {aiAnalysis.recommendations.map((rec, index) => (
                                <ListItem key={index}>
                                  <ListItemText
                                    primary={rec}
                                    primaryTypographyProps={{ variant: 'body2' }}
                                  />
                                </ListItem>
                              ))}
                            </List>
                          </AccordionDetails>
                        </Accordion>
                      )}
                    </Box>
                  ) : (
                    <Typography variant="body2" color="text.secondary">
                      AI analysis will appear here when a task is selected
                    </Typography>
                  )}
                </CardContent>
              </Card>

              {/* Employee Matches Section */}
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    <TrendingUp sx={{ mr: 1, verticalAlign: 'middle', color: 'primary.main' }} />
                    Best Employee Matches
                  </Typography>

                  {matchesLoading ? (
                    <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
                      <CircularProgress />
                    </Box>
                  ) : employeeMatches.length === 0 ? (
                    <Typography color="text.secondary" align="center" sx={{ py: 4 }}>
                      No employee matches found for this task
                    </Typography>
                  ) : (
                    <List>
                      {employeeMatches.slice(0, 5).map((match, index) => (
                        <ListItem
                          key={match.user._id}
                          sx={{
                            border: '1px solid',
                            borderColor: 'divider',
                            borderRadius: 1,
                            mb: 1,
                            bgcolor: index === 0 ? 'success.light' : 'background.paper'
                          }}
                        >
                          <ListItemAvatar>
                            <Avatar sx={{
                              bgcolor: index === 0 ? 'success.main' : 'primary.main',
                              fontWeight: 'bold'
                            }}>
                              {index + 1}
                            </Avatar>
                          </ListItemAvatar>
                          <ListItemText
                            primary={
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                <Typography variant="subtitle1" fontWeight="medium">
                                  {match.user.name}
                                </Typography>
                                {index === 0 && (
                                  <Chip label="Best Match" size="small" color="success" />
                                )}
                              </Box>
                            }
                            secondary={
                              <Box>
                                <Typography variant="body2" color="text.secondary">
                                  {match.user.job} • {match.user.department}
                                </Typography>
                                <Box sx={{ display: 'flex', gap: 2, mt: 0.5 }}>
                                  <Chip
                                    label={`${Math.round(match.fitScore)}% Fit`}
                                    size="small"
                                    color={getFitScoreColor(match.fitScore)}
                                    icon={<Speed />}
                                  />
                                  <Chip
                                    label={`~${Math.round(match.estimatedCompletionTime)}h`}
                                    size="small"
                                    variant="outlined"
                                    icon={<Schedule />}
                                  />
                                </Box>
                              </Box>
                            }
                          />
                          <Button
                            variant="contained"
                            size="small"
                            startIcon={<Assignment />}
                            onClick={() => handleAssignTask(match)}
                            color={index === 0 ? 'success' : 'primary'}
                          >
                            Assign
                          </Button>
                        </ListItem>
                      ))}
                    </List>
                  )}
                </CardContent>
              </Card>
            </Box>
          ) : (
            <Card>
              <CardContent>
                <Box sx={{ textAlign: 'center', py: 8 }}>
                  <AutoAwesome sx={{ fontSize: 60, color: 'text.secondary', mb: 2, opacity: 0.5 }} />
                  <Typography variant="h6" color="text.secondary" gutterBottom>
                    Select a Task for AI Analysis
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Choose an unassigned task from the list to see AI-powered employee recommendations
                    and intelligent matching based on skills, performance, and availability.
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          )}
        </Grid>
      </Grid>

      {/* Assignment Confirmation Dialog */}
      <Dialog
        open={assignDialogOpen}
        onClose={() => setAssignDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Confirm Task Assignment</DialogTitle>
        <DialogContent>
          <Typography gutterBottom>
            Assign "{selectedTask?.title}" to {selectedEmployee?.user?.name}?
          </Typography>

          {selectedEmployee && (
            <Box sx={{ mt: 2, p: 2, bgcolor: 'action.hover', borderRadius: 1 }}>
              <Typography variant="subtitle2" gutterBottom>
                Assignment Details:
              </Typography>
              <Typography variant="body2">
                <strong>Employee:</strong> {selectedEmployee.user.name}
              </Typography>
              <Typography variant="body2">
                <strong>Position:</strong> {selectedEmployee.user.job}
              </Typography>
              <Typography variant="body2">
                <strong>Department:</strong> {selectedEmployee.user.department}
              </Typography>
              <Typography variant="body2">
                <strong>Fit Score:</strong> {Math.round(selectedEmployee.fitScore)}%
              </Typography>
              <Typography variant="body2">
                <strong>Estimated Time:</strong> {Math.round(selectedEmployee.estimatedCompletionTime)} hours
              </Typography>
            </Box>
          )}

          <TextField
            fullWidth
            label="Assignment Reason (Optional)"
            multiline
            rows={3}
            value={assignmentReason}
            onChange={(e) => setAssignmentReason(e.target.value)}
            sx={{ mt: 2 }}
            placeholder="Enter reason for this assignment..."
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAssignDialogOpen(false)}>
            Cancel
          </Button>
          <Button
            onClick={confirmAssignment}
            variant="contained"
            disabled={loading}
            startIcon={loading ? <CircularProgress size={20} /> : <CheckCircle />}
          >
            {loading ? 'Assigning...' : 'Confirm Assignment'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default GEKTaskAssignment;
