const LeaveRequest = require('../../models/LeaveRequest');
const Task = require('../../models/Task');
const User = require('../../models/User');
const Attendance = require('../../models/Attendance');

class LeaveAnalyzer {
  constructor() {
    this.leaveTypes = {
      'vacation': { maxDays: 25, requiresAdvanceNotice: 7 },
      'sick': { maxDays: 10, requiresAdvanceNotice: 0 },
      'personal': { maxDays: 5, requiresAdvanceNotice: 3 },
      'maternity': { maxDays: 90, requiresAdvanceNotice: 30 },
      'paternity': { maxDays: 14, requiresAdvanceNotice: 30 },
      'emergency': { maxDays: 3, requiresAdvanceNotice: 0 }
    };
  }

  /**
   * Analyze leave request for conflicts and suggestions
   * @param {string} userId - User ID
   * @param {Object} leaveRequest - Leave request details
   * @returns {Promise<Object>} - Analysis result
   */
  async analyzeLeaveRequest(userId, leaveRequest) {
    try {
      const { startDate, endDate, leaveType, reason } = leaveRequest;
      
      const analysis = {
        conflicts: [],
        suggestions: [],
        recommendations: [],
        approval: {
          recommended: true,
          confidence: 0.8,
          reasons: []
        },
        alternatives: []
      };

      // Check task conflicts
      const taskConflicts = await this.checkTaskConflicts(userId, startDate, endDate);
      if (taskConflicts.length > 0) {
        analysis.conflicts.push({
          type: 'task_deadline',
          severity: 'high',
          details: taskConflicts,
          message: `You have ${taskConflicts.length} task(s) with deadlines during your requested leave period`
        });
        analysis.approval.recommended = false;
        analysis.approval.confidence -= 0.3;
      }

      // Check team workload
      const teamWorkload = await this.checkTeamWorkload(userId, startDate, endDate);
      if (teamWorkload.overloaded) {
        analysis.conflicts.push({
          type: 'team_workload',
          severity: 'medium',
          details: teamWorkload,
          message: 'Your team will be understaffed during this period'
        });
        analysis.approval.confidence -= 0.2;
      }

      // Check leave balance
      const balanceCheck = await this.checkLeaveBalance(userId, leaveType, startDate, endDate);
      if (!balanceCheck.sufficient) {
        analysis.conflicts.push({
          type: 'insufficient_balance',
          severity: 'high',
          details: balanceCheck,
          message: `Insufficient ${leaveType} leave balance. You have ${balanceCheck.remaining} days remaining.`
        });
        analysis.approval.recommended = false;
        analysis.approval.confidence = 0.1;
      }

      // Check company blackout dates
      const blackoutConflicts = await this.checkBlackoutDates(startDate, endDate);
      if (blackoutConflicts.length > 0) {
        analysis.conflicts.push({
          type: 'blackout_period',
          severity: 'high',
          details: blackoutConflicts,
          message: 'Requested dates fall during company blackout periods'
        });
        analysis.approval.recommended = false;
        analysis.approval.confidence -= 0.4;
      }

      // Generate suggestions for better dates
      if (analysis.conflicts.length > 0) {
        const alternatives = await this.suggestAlternativeDates(userId, leaveRequest);
        analysis.alternatives = alternatives;
      }

      // Add recommendations
      analysis.recommendations = await this.generateRecommendations(userId, leaveRequest, analysis);

      return analysis;
    } catch (error) {
      console.error('Error analyzing leave request:', error);
      throw error;
    }
  }

  /**
   * Check for task deadline conflicts
   * @param {string} userId - User ID
   * @param {Date} startDate - Leave start date
   * @param {Date} endDate - Leave end date
   * @returns {Promise<Array>} - Conflicting tasks
   */
  async checkTaskConflicts(userId, startDate, endDate) {
    try {
      const conflicts = await Task.find({
        assignedTo: userId,
        status: { $in: ['Pending', 'In Progress'] },
        deadline: {
          $gte: new Date(startDate),
          $lte: new Date(endDate)
        }
      }).select('title deadline priority category');

      return conflicts.map(task => ({
        taskId: task._id,
        title: task.title,
        deadline: task.deadline,
        priority: task.priority,
        category: task.category,
        daysUntilDeadline: Math.ceil((new Date(task.deadline) - new Date(startDate)) / (1000 * 60 * 60 * 24))
      }));
    } catch (error) {
      console.error('Error checking task conflicts:', error);
      return [];
    }
  }

  /**
   * Check team workload during leave period
   * @param {string} userId - User ID
   * @param {Date} startDate - Leave start date
   * @param {Date} endDate - Leave end date
   * @returns {Promise<Object>} - Team workload analysis
   */
  async checkTeamWorkload(userId, startDate, endDate) {
    try {
      // Get user's department
      const user = await User.findById(userId).select('department');
      if (!user) return { overloaded: false };

      // Get team members in same department
      const teamMembers = await User.find({
        department: user.department,
        _id: { $ne: userId },
        active: true
      }).select('_id name');

      // Check if other team members have leave during this period
      const overlappingLeaves = await LeaveRequest.find({
        userId: { $in: teamMembers.map(m => m._id) },
        status: 'Approved',
        $or: [
          {
            startDate: { $lte: new Date(endDate) },
            endDate: { $gte: new Date(startDate) }
          }
        ]
      });

      // Calculate team availability
      const totalTeamSize = teamMembers.length + 1; // Including the requesting user
      const membersOnLeave = overlappingLeaves.length + 1; // Including the requesting user
      const availabilityPercentage = ((totalTeamSize - membersOnLeave) / totalTeamSize) * 100;

      return {
        overloaded: availabilityPercentage < 60, // Less than 60% team availability
        teamSize: totalTeamSize,
        membersOnLeave,
        availabilityPercentage,
        overlappingLeaves: overlappingLeaves.map(leave => ({
          userId: leave.userId,
          startDate: leave.startDate,
          endDate: leave.endDate,
          leaveType: leave.leaveType
        }))
      };
    } catch (error) {
      console.error('Error checking team workload:', error);
      return { overloaded: false };
    }
  }

  /**
   * Check leave balance for user
   * @param {string} userId - User ID
   * @param {string} leaveType - Type of leave
   * @param {Date} startDate - Leave start date
   * @param {Date} endDate - Leave end date
   * @returns {Promise<Object>} - Balance check result
   */
  async checkLeaveBalance(userId, leaveType, startDate, endDate) {
    try {
      // Calculate requested days
      const requestedDays = Math.ceil((new Date(endDate) - new Date(startDate)) / (1000 * 60 * 60 * 24)) + 1;

      // Get current year's leave requests
      const currentYear = new Date().getFullYear();
      const yearStart = new Date(currentYear, 0, 1);
      const yearEnd = new Date(currentYear, 11, 31);

      const usedLeaves = await LeaveRequest.find({
        userId,
        leaveType,
        status: { $in: ['Approved', 'Pending'] },
        startDate: { $gte: yearStart, $lte: yearEnd }
      });

      const usedDays = usedLeaves.reduce((total, leave) => {
        const days = Math.ceil((new Date(leave.endDate) - new Date(leave.startDate)) / (1000 * 60 * 60 * 24)) + 1;
        return total + days;
      }, 0);

      const maxAllowed = this.leaveTypes[leaveType]?.maxDays || 0;
      const remaining = maxAllowed - usedDays;

      return {
        sufficient: remaining >= requestedDays,
        requested: requestedDays,
        used: usedDays,
        remaining,
        maxAllowed
      };
    } catch (error) {
      console.error('Error checking leave balance:', error);
      return { sufficient: false, remaining: 0 };
    }
  }

  /**
   * Check for company blackout dates
   * @param {Date} startDate - Leave start date
   * @param {Date} endDate - Leave end date
   * @returns {Promise<Array>} - Blackout date conflicts
   */
  async checkBlackoutDates(startDate, endDate) {
    // Define company blackout periods (this could be stored in database)
    const blackoutPeriods = [
      {
        name: 'Year-end closing',
        startDate: new Date(new Date().getFullYear(), 11, 20), // Dec 20
        endDate: new Date(new Date().getFullYear(), 11, 31)    // Dec 31
      },
      {
        name: 'Quarter-end processing',
        startDate: new Date(new Date().getFullYear(), 2, 28),  // Mar 28
        endDate: new Date(new Date().getFullYear(), 2, 31)     // Mar 31
      }
      // Add more blackout periods as needed
    ];

    const conflicts = [];
    const requestStart = new Date(startDate);
    const requestEnd = new Date(endDate);

    blackoutPeriods.forEach(period => {
      if (requestStart <= period.endDate && requestEnd >= period.startDate) {
        conflicts.push({
          name: period.name,
          startDate: period.startDate,
          endDate: period.endDate,
          overlapDays: Math.min(requestEnd, period.endDate) - Math.max(requestStart, period.startDate) + 1
        });
      }
    });

    return conflicts;
  }

  /**
   * Suggest alternative dates for leave
   * @param {string} userId - User ID
   * @param {Object} leaveRequest - Original leave request
   * @returns {Promise<Array>} - Alternative date suggestions
   */
  async suggestAlternativeDates(userId, leaveRequest) {
    try {
      const { startDate, endDate, leaveType } = leaveRequest;
      const requestedDays = Math.ceil((new Date(endDate) - new Date(startDate)) / (1000 * 60 * 60 * 24)) + 1;
      
      const alternatives = [];
      const currentDate = new Date();
      
      // Look for alternative periods in the next 6 months
      for (let weekOffset = 1; weekOffset <= 26; weekOffset++) {
        const altStartDate = new Date(currentDate);
        altStartDate.setDate(currentDate.getDate() + (weekOffset * 7));
        
        const altEndDate = new Date(altStartDate);
        altEndDate.setDate(altStartDate.getDate() + requestedDays - 1);

        // Skip if dates are in the past
        if (altStartDate < currentDate) continue;

        // Analyze this alternative period
        const analysis = await this.analyzeLeaveRequest(userId, {
          startDate: altStartDate,
          endDate: altEndDate,
          leaveType
        });

        if (analysis.approval.recommended && analysis.conflicts.length === 0) {
          alternatives.push({
            startDate: altStartDate,
            endDate: altEndDate,
            confidence: analysis.approval.confidence,
            benefits: [
              'No task deadline conflicts',
              'Good team availability',
              'No blackout period conflicts'
            ]
          });

          // Stop after finding 3 good alternatives
          if (alternatives.length >= 3) break;
        }
      }

      return alternatives;
    } catch (error) {
      console.error('Error suggesting alternative dates:', error);
      return [];
    }
  }

  /**
   * Generate recommendations based on analysis
   * @param {string} userId - User ID
   * @param {Object} leaveRequest - Leave request
   * @param {Object} analysis - Analysis results
   * @returns {Promise<Array>} - Recommendations
   */
  async generateRecommendations(userId, leaveRequest, analysis) {
    const recommendations = [];

    // Task-related recommendations
    if (analysis.conflicts.some(c => c.type === 'task_deadline')) {
      recommendations.push({
        type: 'task_management',
        priority: 'high',
        title: 'Complete or delegate tasks before leave',
        description: 'Consider completing urgent tasks or delegating them to team members before your leave starts.',
        actions: [
          'Review task deadlines',
          'Delegate tasks to available team members',
          'Request deadline extensions if possible'
        ]
      });
    }

    // Team workload recommendations
    if (analysis.conflicts.some(c => c.type === 'team_workload')) {
      recommendations.push({
        type: 'team_coordination',
        priority: 'medium',
        title: 'Coordinate with team for coverage',
        description: 'Ensure adequate team coverage during your absence.',
        actions: [
          'Discuss workload distribution with team',
          'Create handover documentation',
          'Set up out-of-office notifications'
        ]
      });
    }

    // Balance-related recommendations
    if (analysis.conflicts.some(c => c.type === 'insufficient_balance')) {
      recommendations.push({
        type: 'leave_planning',
        priority: 'high',
        title: 'Consider shorter leave duration',
        description: 'Your current leave balance may not cover the full requested period.',
        actions: [
          'Reduce leave duration',
          'Consider unpaid leave options',
          'Plan leave for next year when balance resets'
        ]
      });
    }

    // General recommendations for optimal leave
    if (analysis.approval.recommended) {
      recommendations.push({
        type: 'preparation',
        priority: 'low',
        title: 'Prepare for smooth leave transition',
        description: 'Follow best practices for leave preparation.',
        actions: [
          'Update project status documentation',
          'Brief team members on ongoing work',
          'Set up automatic email responses',
          'Ensure all urgent tasks are completed'
        ]
      });
    }

    return recommendations;
  }

  /**
   * Get optimal leave suggestions for user
   * @param {string} userId - User ID
   * @param {Object} preferences - User preferences
   * @returns {Promise<Array>} - Optimal leave suggestions
   */
  async getOptimalLeaveSuggestions(userId, preferences = {}) {
    try {
      const {
        leaveType = 'vacation',
        duration = 5,
        timeframe = 'next_3_months'
      } = preferences;

      const suggestions = [];
      const currentDate = new Date();
      let endDate = new Date();

      // Set timeframe
      switch (timeframe) {
        case 'next_month':
          endDate.setMonth(currentDate.getMonth() + 1);
          break;
        case 'next_3_months':
          endDate.setMonth(currentDate.getMonth() + 3);
          break;
        case 'next_6_months':
          endDate.setMonth(currentDate.getMonth() + 6);
          break;
        default:
          endDate.setMonth(currentDate.getMonth() + 3);
      }

      // Analyze workload patterns
      const workloadAnalysis = await this.analyzeWorkloadPatterns(userId);
      
      // Find optimal periods with low workload
      const optimalPeriods = workloadAnalysis.lowWorkloadPeriods.filter(period => {
        return period.startDate >= currentDate && period.endDate <= endDate;
      });

      for (const period of optimalPeriods) {
        const leaveStart = new Date(period.startDate);
        const leaveEnd = new Date(leaveStart);
        leaveEnd.setDate(leaveStart.getDate() + duration - 1);

        if (leaveEnd <= period.endDate) {
          const analysis = await this.analyzeLeaveRequest(userId, {
            startDate: leaveStart,
            endDate: leaveEnd,
            leaveType
          });

          if (analysis.approval.recommended) {
            suggestions.push({
              startDate: leaveStart,
              endDate: leaveEnd,
              duration,
              confidence: analysis.approval.confidence,
              benefits: [
                `Low workload period (${period.workloadLevel}% capacity)`,
                'Good team availability',
                'No major deadlines'
              ],
              workloadLevel: period.workloadLevel
            });
          }
        }
      }

      // Sort by confidence and workload level
      suggestions.sort((a, b) => {
        if (b.confidence !== a.confidence) {
          return b.confidence - a.confidence;
        }
        return a.workloadLevel - b.workloadLevel;
      });

      return suggestions.slice(0, 5); // Return top 5 suggestions
    } catch (error) {
      console.error('Error getting optimal leave suggestions:', error);
      return [];
    }
  }

  /**
   * Analyze user's workload patterns
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Workload analysis
   */
  async analyzeWorkloadPatterns(userId) {
    try {
      const currentDate = new Date();
      const futureDate = new Date();
      futureDate.setMonth(currentDate.getMonth() + 6);

      // Get user's tasks for the next 6 months
      const tasks = await Task.find({
        assignedTo: userId,
        status: { $in: ['Pending', 'In Progress'] },
        deadline: { $gte: currentDate, $lte: futureDate }
      }).sort({ deadline: 1 });

      // Analyze workload by week
      const weeklyWorkload = {};
      const lowWorkloadPeriods = [];

      tasks.forEach(task => {
        const weekKey = this.getWeekKey(task.deadline);
        if (!weeklyWorkload[weekKey]) {
          weeklyWorkload[weekKey] = { taskCount: 0, highPriorityCount: 0 };
        }
        weeklyWorkload[weekKey].taskCount++;
        if (task.priority === 'High' || task.priority === 'Urgent') {
          weeklyWorkload[weekKey].highPriorityCount++;
        }
      });

      // Identify low workload periods
      const weeks = Object.keys(weeklyWorkload).sort();
      for (let i = 0; i < weeks.length; i++) {
        const week = weeks[i];
        const workload = weeklyWorkload[week];
        const workloadLevel = (workload.taskCount * 20) + (workload.highPriorityCount * 30);

        if (workloadLevel < 50) { // Low workload threshold
          const weekStart = this.getDateFromWeekKey(week);
          const weekEnd = new Date(weekStart);
          weekEnd.setDate(weekStart.getDate() + 6);

          lowWorkloadPeriods.push({
            startDate: weekStart,
            endDate: weekEnd,
            workloadLevel,
            taskCount: workload.taskCount,
            highPriorityCount: workload.highPriorityCount
          });
        }
      }

      return {
        weeklyWorkload,
        lowWorkloadPeriods,
        averageWorkload: Object.values(weeklyWorkload).reduce((sum, w) => sum + w.taskCount, 0) / weeks.length
      };
    } catch (error) {
      console.error('Error analyzing workload patterns:', error);
      return { weeklyWorkload: {}, lowWorkloadPeriods: [], averageWorkload: 0 };
    }
  }

  /**
   * Get week key for date grouping
   * @param {Date} date - Date
   * @returns {string} - Week key
   */
  getWeekKey(date) {
    const year = date.getFullYear();
    const week = Math.ceil(((date - new Date(year, 0, 1)) / 86400000 + 1) / 7);
    return `${year}-W${week.toString().padStart(2, '0')}`;
  }

  /**
   * Get date from week key
   * @param {string} weekKey - Week key
   * @returns {Date} - Start date of week
   */
  getDateFromWeekKey(weekKey) {
    const [year, week] = weekKey.split('-W');
    const yearStart = new Date(parseInt(year), 0, 1);
    const weekStart = new Date(yearStart);
    weekStart.setDate(yearStart.getDate() + (parseInt(week) - 1) * 7);
    return weekStart;
  }
}

// Singleton instance
const leaveAnalyzer = new LeaveAnalyzer();

module.exports = leaveAnalyzer;
