/**
 * Smart Assistant Routes
 * API endpoints for AI insights and user activity tracking
 */

const express = require('express');
const router = express.Router();
const { authenticate } = require('../middleware/authmiddleware');
const {
  trackActivity,
  getUserInsights,
  getActivityStats,
  provideFeedback,
  markInsightViewed,
  dismissInsight,
  createTestInsights
} = require('../controllers/smartAssistantController');

// Apply authentication middleware to all routes
router.use(authenticate);

// Activity tracking routes
router.post('/activity', trackActivity);
router.get('/activity/stats', getActivityStats);

// Insights routes
router.get('/insights', getUserInsights);
router.post('/insights/:insightId/feedback', provideFeedback);
router.post('/insights/:insightId/viewed', markInsightViewed);
router.post('/insights/:insightId/dismiss', dismissInsight);

// Development/testing routes
router.post('/test/create-insights', createTestInsights);

module.exports = router;
