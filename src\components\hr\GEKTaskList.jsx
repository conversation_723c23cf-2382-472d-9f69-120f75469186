import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  Chip,
  IconButton,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Card,
  CardContent,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  Alert,
  Tooltip,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar
} from '@mui/material';
import {
  Search,
  FilterList,
  Assignment,
  Person,
  Schedule,
  PriorityHigh,
  Refresh,
  SwapHoriz,
  Visibility,
  Close,
  TrendingUp
} from '@mui/icons-material';
import { format } from 'date-fns';
import { showSuccessToast, showErrorToast, TOAST_CATEGORIES } from '../../Utils/toastUtils';
import GEKService from '../../Services/GEKService';

/**
 * GEK Task List Component
 * Displays available tasks with GEK-based employee matching and reassignment capabilities
 */
const GEKTaskList = () => {
  // State management
  const [tasks, setTasks] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState({
    status: '',
    category: '',
    priority: ''
  });

  // Dialog states
  const [selectedTask, setSelectedTask] = useState(null);
  const [employeeMatches, setEmployeeMatches] = useState([]);
  const [matchDialogOpen, setMatchDialogOpen] = useState(false);
  const [reassignDialogOpen, setReassignDialogOpen] = useState(false);
  const [selectedEmployee, setSelectedEmployee] = useState(null);
  const [reassignReason, setReassignReason] = useState('');

  // Constants
  const taskStatuses = ['Assigned', 'In Progress', 'Completed', 'Needs Revision'];
  const taskCategories = [
    'General', 'Development', 'Design', 'Marketing', 'HR',
    'Finance', 'Operations', 'Project', 'Administrative',
    'Training', 'Evaluation', 'Other'
  ];
  const taskPriorities = ['Low', 'Medium', 'High', 'Urgent'];

  // Load tasks on component mount
  useEffect(() => {
    loadTasks();
  }, []);

  // Load tasks with current filters
  const loadTasks = async () => {
    setLoading(true);
    setError(null);

    try {
      const params = {
        search: searchQuery,
        ...filters,
        limit: 50
      };

      // Remove empty filters
      Object.keys(params).forEach(key => {
        if (!params[key]) delete params[key];
      });

      const response = await GEKService.getAvailableTasks(params);
      setTasks(response.tasks || []);
    } catch (err) {
      console.error('Error loading tasks:', err);
      setError('Failed to load tasks');
      showErrorToast('Failed to load tasks', TOAST_CATEGORIES.TASK, 'taskLoadFailed');
    } finally {
      setLoading(false);
    }
  };

  // Handle search
  const handleSearch = (event) => {
    setSearchQuery(event.target.value);
  };

  // Handle filter changes
  const handleFilterChange = (field) => (event) => {
    setFilters({
      ...filters,
      [field]: event.target.value
    });
  };

  // Apply filters
  const applyFilters = () => {
    loadTasks();
  };

  // Clear filters
  const clearFilters = () => {
    setSearchQuery('');
    setFilters({
      status: '',
      category: '',
      priority: ''
    });
    setTimeout(loadTasks, 100);
  };

  // Handle view employee matches
  const handleViewMatches = async (task) => {
    setSelectedTask(task);
    setLoading(true);
    setError(null);

    try {
      const response = await GEKService.getTaskEmployeeMatches(task._id);
      setEmployeeMatches(response.rankedEmployees || []);
      setMatchDialogOpen(true);
    } catch (err) {
      console.error('Error getting employee matches:', err);
      setError('Failed to get employee matches');
      showErrorToast('Failed to get employee matches', TOAST_CATEGORIES.TASK, 'matchesFailed');
    } finally {
      setLoading(false);
    }
  };

  // Handle reassign task
  const handleReassignTask = (employee) => {
    setSelectedEmployee(employee);
    setMatchDialogOpen(false);
    setReassignDialogOpen(true);
  };

  // Confirm reassignment
  const confirmReassignment = async () => {
    if (!selectedEmployee || !selectedTask) return;

    setLoading(true);
    try {
      await GEKService.reassignTask(
        selectedTask._id,
        selectedEmployee.user._id,
        reassignReason
      );

      showSuccessToast(
        `Task reassigned to ${selectedEmployee.user.name}`,
        TOAST_CATEGORIES.TASK,
        'taskReassigned'
      );

      // Refresh tasks
      loadTasks();
      
      // Close dialogs
      setReassignDialogOpen(false);
      setSelectedEmployee(null);
      setSelectedTask(null);
      setReassignReason('');
    } catch (err) {
      console.error('Error reassigning task:', err);
      showErrorToast('Failed to reassign task', TOAST_CATEGORIES.TASK, 'reassignFailed');
    } finally {
      setLoading(false);
    }
  };

  // Get priority color
  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'Urgent': return 'error';
      case 'High': return 'warning';
      case 'Medium': return 'info';
      case 'Low': return 'success';
      default: return 'default';
    }
  };

  // Get status color
  const getStatusColor = (status) => {
    switch (status) {
      case 'Completed': return 'success';
      case 'In Progress': return 'info';
      case 'Needs Revision': return 'warning';
      case 'Assigned': return 'default';
      default: return 'default';
    }
  };

  return (
    <Box>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom fontWeight={700}>
          GEK Task Management
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Manage task assignments with AI-powered employee matching and performance insights.
        </Typography>
      </Box>

      {/* Error Alert */}
      {error && (
        <Alert
          severity="error"
          sx={{ mb: 3 }}
          action={
            <Button color="inherit" size="small" onClick={loadTasks}>
              Retry
            </Button>
          }
        >
          {error}
        </Alert>
      )}

      {/* Filters and Search */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                placeholder="Search tasks..."
                value={searchQuery}
                onChange={handleSearch}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Search />
                    </InputAdornment>
                  )
                }}
              />
            </Grid>
            <Grid item xs={12} md={2}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  value={filters.status}
                  label="Status"
                  onChange={handleFilterChange('status')}
                >
                  <MenuItem value="">All</MenuItem>
                  {taskStatuses.map((status) => (
                    <MenuItem key={status} value={status}>{status}</MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <FormControl fullWidth>
                <InputLabel>Category</InputLabel>
                <Select
                  value={filters.category}
                  label="Category"
                  onChange={handleFilterChange('category')}
                >
                  <MenuItem value="">All</MenuItem>
                  {taskCategories.map((category) => (
                    <MenuItem key={category} value={category}>{category}</MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <FormControl fullWidth>
                <InputLabel>Priority</InputLabel>
                <Select
                  value={filters.priority}
                  label="Priority"
                  onChange={handleFilterChange('priority')}
                >
                  <MenuItem value="">All</MenuItem>
                  {taskPriorities.map((priority) => (
                    <MenuItem key={priority} value={priority}>{priority}</MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={3}>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Button
                  variant="contained"
                  onClick={applyFilters}
                  startIcon={<FilterList />}
                  disabled={loading}
                >
                  Apply
                </Button>
                <Button
                  variant="outlined"
                  onClick={clearFilters}
                  disabled={loading}
                >
                  Clear
                </Button>
                <IconButton onClick={loadTasks} disabled={loading}>
                  <Refresh />
                </IconButton>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Tasks Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Task</TableCell>
              <TableCell>Assigned To</TableCell>
              <TableCell>Category</TableCell>
              <TableCell>Priority</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Deadline</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={7} align="center" sx={{ py: 4 }}>
                  <CircularProgress />
                </TableCell>
              </TableRow>
            ) : tasks.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} align="center" sx={{ py: 4 }}>
                  <Typography color="text.secondary">
                    No tasks found matching your criteria
                  </Typography>
                </TableCell>
              </TableRow>
            ) : (
              tasks.map((task) => (
                <TableRow key={task._id} hover>
                  <TableCell>
                    <Box>
                      <Typography variant="subtitle2" fontWeight="medium">
                        {task.title}
                      </Typography>
                      <Typography variant="body2" color="text.secondary" noWrap>
                        {task.description?.substring(0, 60)}
                        {task.description?.length > 60 ? '...' : ''}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Avatar sx={{ width: 32, height: 32 }}>
                        {task.assignedTo?.name?.charAt(0)}
                      </Avatar>
                      <Box>
                        <Typography variant="body2" fontWeight="medium">
                          {task.assignedTo?.name}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {task.assignedTo?.job}
                        </Typography>
                      </Box>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={task.category}
                      size="small"
                      variant="outlined"
                    />
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={task.priority}
                      size="small"
                      color={getPriorityColor(task.priority)}
                    />
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={task.status}
                      size="small"
                      color={getStatusColor(task.status)}
                      variant="outlined"
                    />
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {format(new Date(task.deadline), 'MMM dd, yyyy')}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {new Date(task.deadline) < new Date() ? 'Overdue' :
                       Math.ceil((new Date(task.deadline) - new Date()) / (1000 * 60 * 60 * 24)) + ' days left'}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <Tooltip title="View Best Matches">
                        <IconButton
                          size="small"
                          onClick={() => handleViewMatches(task)}
                          color="primary"
                        >
                          <TrendingUp />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="View Task Details">
                        <IconButton
                          size="small"
                          color="info"
                        >
                          <Visibility />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Employee Matches Dialog */}
      <Dialog
        open={matchDialogOpen}
        onClose={() => setMatchDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Typography variant="h6">
              Best Employee Matches for "{selectedTask?.title}"
            </Typography>
            <IconButton onClick={() => setMatchDialogOpen(false)}>
              <Close />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent>
          {selectedTask && (
            <Box sx={{ mb: 3 }}>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Task Details
              </Typography>
              <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
                <Chip label={selectedTask.category} size="small" />
                <Chip label={selectedTask.priority} size="small" color={getPriorityColor(selectedTask.priority)} />
                <Chip label={`Due: ${format(new Date(selectedTask.deadline), 'MMM dd')}`} size="small" variant="outlined" />
              </Box>
            </Box>
          )}

          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
              <CircularProgress />
            </Box>
          ) : employeeMatches.length === 0 ? (
            <Typography color="text.secondary" align="center" sx={{ py: 4 }}>
              No employee matches found
            </Typography>
          ) : (
            <List>
              {employeeMatches.map((match, index) => (
                <ListItem
                  key={match.user._id}
                  sx={{
                    border: '1px solid',
                    borderColor: 'divider',
                    borderRadius: 1,
                    mb: 1,
                    bgcolor: index < 3 ? 'action.hover' : 'background.paper'
                  }}
                >
                  <ListItemAvatar>
                    <Avatar sx={{ bgcolor: index < 3 ? 'secondary.main' : 'primary.main' }}>
                      {index + 1}
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="subtitle1" fontWeight="medium">
                          {match.user.name}
                        </Typography>
                        {index < 3 && (
                          <Chip label="Top Match" size="small" color="secondary" />
                        )}
                      </Box>
                    }
                    secondary={
                      <Box>
                        <Typography variant="body2" color="text.secondary">
                          {match.user.job} • {match.user.department}
                        </Typography>
                        <Box sx={{ display: 'flex', gap: 2, mt: 1 }}>
                          <Typography variant="caption">
                            Fit Score: <strong>{Math.round(match.fitScore)}%</strong>
                          </Typography>
                          <Typography variant="caption">
                            Est. Time: <strong>{Math.round(match.estimatedCompletionTime)}h</strong>
                          </Typography>
                          <Typography variant="caption">
                            Confidence: <strong>{Math.round(match.confidenceScore)}%</strong>
                          </Typography>
                        </Box>
                      </Box>
                    }
                  />
                  <Button
                    variant="outlined"
                    size="small"
                    startIcon={<SwapHoriz />}
                    onClick={() => handleReassignTask(match)}
                    disabled={match.user._id === selectedTask?.assignedTo?._id}
                  >
                    {match.user._id === selectedTask?.assignedTo?._id ? 'Current' : 'Reassign'}
                  </Button>
                </ListItem>
              ))}
            </List>
          )}
        </DialogContent>
      </Dialog>

      {/* Reassign Confirmation Dialog */}
      <Dialog
        open={reassignDialogOpen}
        onClose={() => setReassignDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Confirm Task Reassignment</DialogTitle>
        <DialogContent>
          <Typography gutterBottom>
            Are you sure you want to reassign "{selectedTask?.title}" to {selectedEmployee?.user?.name}?
          </Typography>

          {selectedEmployee && (
            <Box sx={{ mt: 2, p: 2, bgcolor: 'action.hover', borderRadius: 1 }}>
              <Typography variant="subtitle2" gutterBottom>
                New Assignee Details:
              </Typography>
              <Typography variant="body2">
                <strong>Name:</strong> {selectedEmployee.user.name}
              </Typography>
              <Typography variant="body2">
                <strong>Position:</strong> {selectedEmployee.user.job}
              </Typography>
              <Typography variant="body2">
                <strong>Fit Score:</strong> {Math.round(selectedEmployee.fitScore)}%
              </Typography>
              <Typography variant="body2">
                <strong>Estimated Completion:</strong> {Math.round(selectedEmployee.estimatedCompletionTime)} hours
              </Typography>
            </Box>
          )}

          <TextField
            fullWidth
            label="Reason for Reassignment (Optional)"
            multiline
            rows={3}
            value={reassignReason}
            onChange={(e) => setReassignReason(e.target.value)}
            sx={{ mt: 2 }}
            placeholder="Enter reason for reassignment..."
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setReassignDialogOpen(false)}>
            Cancel
          </Button>
          <Button
            onClick={confirmReassignment}
            variant="contained"
            disabled={loading}
          >
            {loading ? <CircularProgress size={20} /> : 'Confirm Reassignment'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default GEKTaskList;
